"""
Technical Indicators
Comprehensive technical analysis indicators with optimized calculations.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import structlog
import numpy as np
import pandas as pd
from math import sqrt, log

logger = structlog.get_logger(__name__)


class TechnicalIndicators:
    """
    Professional-grade technical indicators with efficient calculations.
    
    Supported Indicators:
    - Moving Averages (SMA, EMA, WMA)
    - Momentum Oscillators (RSI, MACD, Stochastic)
    - Volatility Indicators (Bollinger Bands, ATR)
    - Volume Indicators (OBV, Volume Profile)
    - Trend Indicators (ADX, Parabolic SAR)
    """
    
    def __init__(self):
        """Initialize technical indicators calculator."""
        self.indicator_cache: Dict[str, Dict] = {}
        self.cache_expiry = timedelta(minutes=5)  # Cache for 5 minutes
        
        logger.info("✅ Technical indicators initialized")
    
    async def calculate_all_indicators(
        self,
        symbol: str,
        current_price: float,
        price_history: Optional[List[float]] = None,
        volume_history: Optional[List[int]] = None
    ) -> Dict[str, float]:
        """Calculate all technical indicators for a symbol."""
        try:
            # Check cache first
            cached_indicators = self._get_cached_indicators(symbol)
            if cached_indicators:
                return cached_indicators
            
            # Generate sample price history if not provided (for demo)
            if not price_history:
                price_history = self._generate_sample_price_history(current_price)
            
            if not volume_history:
                volume_history = self._generate_sample_volume_history(len(price_history))
            
            # Convert to pandas for easier calculation
            df = pd.DataFrame({
                'close': price_history,
                'volume': volume_history
            })
            
            # Calculate all indicators
            indicators = {}
            
            # Moving Averages
            indicators.update(await self._calculate_moving_averages(df))
            
            # Momentum Oscillators
            indicators.update(await self._calculate_momentum_oscillators(df))
            
            # Volatility Indicators
            indicators.update(await self._calculate_volatility_indicators(df))
            
            # Volume Indicators
            indicators.update(await self._calculate_volume_indicators(df))
            
            # Trend Indicators
            indicators.update(await self._calculate_trend_indicators(df))
            
            # Cache the results
            self._cache_indicators(symbol, indicators)
            
            logger.debug(f"📊 Technical indicators calculated", symbol=symbol, count=len(indicators))
            
            return indicators
            
        except Exception as e:
            logger.error(f"❌ Error calculating indicators for {symbol}", error=str(e))
            return self._get_default_indicators()
    
    async def _calculate_moving_averages(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate various moving averages."""
        try:
            indicators = {}
            
            # Simple Moving Averages
            for period in [5, 10, 20, 50, 100, 200]:
                if len(df) >= period:
                    sma = df['close'].rolling(window=period).mean().iloc[-1]
                    indicators[f'sma_{period}'] = round(sma, 2)
            
            # Exponential Moving Averages
            for period in [12, 26, 50]:
                if len(df) >= period:
                    ema = df['close'].ewm(span=period).mean().iloc[-1]
                    indicators[f'ema_{period}'] = round(ema, 2)
            
            # Weighted Moving Average
            if len(df) >= 20:
                weights = np.arange(1, 21)
                wma = np.average(df['close'].tail(20), weights=weights)
                indicators['wma_20'] = round(wma, 2)
            
            return indicators
            
        except Exception as e:
            logger.error("❌ Error calculating moving averages", error=str(e))
            return {}
    
    async def _calculate_momentum_oscillators(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate momentum oscillators."""
        try:
            indicators = {}
            
            # RSI (Relative Strength Index)
            if len(df) >= 14:
                delta = df['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                indicators['rsi'] = round(rsi.iloc[-1], 2)
            
            # MACD (Moving Average Convergence Divergence)
            if len(df) >= 26:
                ema_12 = df['close'].ewm(span=12).mean()
                ema_26 = df['close'].ewm(span=26).mean()
                macd_line = ema_12 - ema_26
                signal_line = macd_line.ewm(span=9).mean()
                histogram = macd_line - signal_line
                
                indicators['macd'] = round(macd_line.iloc[-1], 4)
                indicators['macd_signal'] = round(signal_line.iloc[-1], 4)
                indicators['macd_histogram'] = round(histogram.iloc[-1], 4)
            
            # Stochastic Oscillator
            if len(df) >= 14:
                low_14 = df['close'].rolling(window=14).min()
                high_14 = df['close'].rolling(window=14).max()
                k_percent = 100 * ((df['close'] - low_14) / (high_14 - low_14))
                d_percent = k_percent.rolling(window=3).mean()
                
                indicators['stoch_k'] = round(k_percent.iloc[-1], 2)
                indicators['stoch_d'] = round(d_percent.iloc[-1], 2)
            
            # Williams %R
            if len(df) >= 14:
                high_14 = df['close'].rolling(window=14).max()
                low_14 = df['close'].rolling(window=14).min()
                williams_r = -100 * ((high_14 - df['close']) / (high_14 - low_14))
                indicators['williams_r'] = round(williams_r.iloc[-1], 2)
            
            return indicators
            
        except Exception as e:
            logger.error("❌ Error calculating momentum oscillators", error=str(e))
            return {}
    
    async def _calculate_volatility_indicators(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate volatility indicators."""
        try:
            indicators = {}
            
            # Bollinger Bands
            if len(df) >= 20:
                sma_20 = df['close'].rolling(window=20).mean()
                std_20 = df['close'].rolling(window=20).std()
                
                bb_upper = sma_20 + (std_20 * 2)
                bb_lower = sma_20 - (std_20 * 2)
                bb_width = (bb_upper - bb_lower) / sma_20 * 100
                
                indicators['bb_upper'] = round(bb_upper.iloc[-1], 2)
                indicators['bb_middle'] = round(sma_20.iloc[-1], 2)
                indicators['bb_lower'] = round(bb_lower.iloc[-1], 2)
                indicators['bb_width'] = round(bb_width.iloc[-1], 2)
                
                # Bollinger Band position
                current_price = df['close'].iloc[-1]
                bb_position = (current_price - bb_lower.iloc[-1]) / (bb_upper.iloc[-1] - bb_lower.iloc[-1])
                indicators['bb_position'] = round(bb_position, 3)
            
            # Average True Range (ATR)
            if len(df) >= 14:
                # Simplified ATR calculation (using only close prices)
                price_changes = df['close'].diff().abs()
                atr = price_changes.rolling(window=14).mean()
                indicators['atr'] = round(atr.iloc[-1], 2)
                
                # ATR percentage
                atr_pct = (atr.iloc[-1] / df['close'].iloc[-1]) * 100
                indicators['atr_pct'] = round(atr_pct, 2)
            
            # Historical Volatility
            if len(df) >= 20:
                returns = df['close'].pct_change().dropna()
                volatility = returns.rolling(window=20).std() * sqrt(252) * 100  # Annualized
                indicators['historical_volatility'] = round(volatility.iloc[-1], 2)
            
            return indicators
            
        except Exception as e:
            logger.error("❌ Error calculating volatility indicators", error=str(e))
            return {}
    
    async def _calculate_volume_indicators(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate volume-based indicators."""
        try:
            indicators = {}
            
            # Volume Moving Average
            if len(df) >= 20:
                volume_ma = df['volume'].rolling(window=20).mean()
                indicators['volume_ma_20'] = round(volume_ma.iloc[-1], 0)
                
                # Volume ratio
                current_volume = df['volume'].iloc[-1]
                volume_ratio = current_volume / volume_ma.iloc[-1]
                indicators['volume_ratio'] = round(volume_ratio, 2)
            
            # On-Balance Volume (OBV)
            if len(df) >= 2:
                price_change = df['close'].diff()
                obv_changes = np.where(price_change > 0, df['volume'], 
                                     np.where(price_change < 0, -df['volume'], 0))
                obv = np.cumsum(obv_changes)
                indicators['obv'] = round(obv[-1], 0)
                
                # OBV trend (simplified)
                if len(obv) >= 10:
                    obv_trend = np.polyfit(range(10), obv[-10:], 1)[0]
                    indicators['obv_trend'] = round(obv_trend, 0)
            
            # Volume Price Trend (VPT)
            if len(df) >= 2:
                price_change_pct = df['close'].pct_change()
                vpt_changes = price_change_pct * df['volume']
                vpt = np.cumsum(vpt_changes.fillna(0))
                indicators['vpt'] = round(vpt[-1], 0)
            
            return indicators
            
        except Exception as e:
            logger.error("❌ Error calculating volume indicators", error=str(e))
            return {}
    
    async def _calculate_trend_indicators(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate trend-following indicators."""
        try:
            indicators = {}
            
            # Average Directional Index (ADX) - Simplified
            if len(df) >= 14:
                # Simplified ADX calculation
                price_changes = df['close'].diff().abs()
                adx_approx = price_changes.rolling(window=14).mean() / df['close'].rolling(window=14).std()
                indicators['adx'] = round(adx_approx.iloc[-1] * 25, 2)  # Scale to typical ADX range
            
            # Parabolic SAR (Simplified)
            if len(df) >= 10:
                # Simplified SAR calculation
                current_price = df['close'].iloc[-1]
                price_range = df['close'].rolling(window=10).max().iloc[-1] - df['close'].rolling(window=10).min().iloc[-1]
                sar_distance = price_range * 0.02  # 2% of recent range
                
                # Assume uptrend for demo
                sar = current_price - sar_distance
                indicators['parabolic_sar'] = round(sar, 2)
            
            # Commodity Channel Index (CCI)
            if len(df) >= 20:
                typical_price = df['close']  # Simplified (normally (H+L+C)/3)
                sma_tp = typical_price.rolling(window=20).mean()
                mad = typical_price.rolling(window=20).apply(lambda x: np.mean(np.abs(x - x.mean())))
                cci = (typical_price - sma_tp) / (0.015 * mad)
                indicators['cci'] = round(cci.iloc[-1], 2)
            
            # Rate of Change (ROC)
            for period in [10, 20]:
                if len(df) >= period + 1:
                    roc = ((df['close'].iloc[-1] - df['close'].iloc[-period-1]) / 
                           df['close'].iloc[-period-1]) * 100
                    indicators[f'roc_{period}'] = round(roc, 2)
            
            return indicators
            
        except Exception as e:
            logger.error("❌ Error calculating trend indicators", error=str(e))
            return {}
    
    def _generate_sample_price_history(self, current_price: float, periods: int = 100) -> List[float]:
        """Generate sample price history for demonstration."""
        try:
            # Generate realistic price movement using random walk
            np.random.seed(42)  # For reproducible results
            
            # Parameters for realistic stock movement
            daily_volatility = 0.02  # 2% daily volatility
            drift = 0.0002  # Slight upward drift
            
            prices = [current_price]
            
            for _ in range(periods - 1):
                # Random walk with drift
                random_change = np.random.normal(drift, daily_volatility)
                new_price = prices[-1] * (1 + random_change)
                prices.insert(0, new_price)  # Insert at beginning for chronological order
            
            return prices
            
        except Exception as e:
            logger.error("❌ Error generating sample price history", error=str(e))
            return [current_price] * periods
    
    def _generate_sample_volume_history(self, periods: int) -> List[int]:
        """Generate sample volume history for demonstration."""
        try:
            # Generate realistic volume pattern
            np.random.seed(42)
            
            base_volume = 1000000  # 1M shares average
            volume_volatility = 0.3  # 30% volume volatility
            
            volumes = []
            for _ in range(periods):
                # Log-normal distribution for volume
                volume_multiplier = np.random.lognormal(0, volume_volatility)
                volume = int(base_volume * volume_multiplier)
                volumes.append(max(100000, volume))  # Minimum 100K volume
            
            return volumes
            
        except Exception as e:
            logger.error("❌ Error generating sample volume history", error=str(e))
            return [1000000] * periods
    
    def _get_cached_indicators(self, symbol: str) -> Optional[Dict[str, float]]:
        """Get cached indicators if still valid."""
        try:
            if symbol not in self.indicator_cache:
                return None
            
            cache_entry = self.indicator_cache[symbol]
            cache_time = cache_entry.get('timestamp')
            
            if not cache_time or datetime.now() - cache_time > self.cache_expiry:
                return None
            
            return cache_entry.get('indicators')
            
        except Exception as e:
            logger.error(f"❌ Error getting cached indicators for {symbol}", error=str(e))
            return None
    
    def _cache_indicators(self, symbol: str, indicators: Dict[str, float]) -> None:
        """Cache calculated indicators."""
        try:
            self.indicator_cache[symbol] = {
                'timestamp': datetime.now(),
                'indicators': indicators
            }
            
            # Clean old cache entries
            self._clean_cache()
            
        except Exception as e:
            logger.error(f"❌ Error caching indicators for {symbol}", error=str(e))
    
    def _clean_cache(self) -> None:
        """Clean expired cache entries."""
        try:
            current_time = datetime.now()
            expired_symbols = []
            
            for symbol, cache_entry in self.indicator_cache.items():
                cache_time = cache_entry.get('timestamp')
                if not cache_time or current_time - cache_time > self.cache_expiry:
                    expired_symbols.append(symbol)
            
            for symbol in expired_symbols:
                del self.indicator_cache[symbol]
            
            if expired_symbols:
                logger.debug(f"🧹 Cleaned {len(expired_symbols)} expired cache entries")
                
        except Exception as e:
            logger.error("❌ Error cleaning indicator cache", error=str(e))
    
    def _get_default_indicators(self) -> Dict[str, float]:
        """Get default indicator values in case of errors."""
        return {
            'sma_20': 100.0,
            'sma_50': 98.0,
            'ema_12': 101.0,
            'ema_26': 99.0,
            'rsi': 50.0,
            'macd': 0.0,
            'macd_signal': 0.0,
            'macd_histogram': 0.0,
            'bb_upper': 105.0,
            'bb_middle': 100.0,
            'bb_lower': 95.0,
            'bb_width': 10.0,
            'atr': 2.0,
            'atr_pct': 2.0,
            'volume_ratio': 1.0,
            'obv': 0.0,
            'adx': 25.0
        }
    
    async def calculate_support_resistance(
        self,
        symbol: str,
        price_history: List[float],
        lookback_periods: int = 50
    ) -> Dict[str, List[float]]:
        """Calculate dynamic support and resistance levels."""
        try:
            if len(price_history) < lookback_periods:
                return {'support_levels': [], 'resistance_levels': []}
            
            prices = np.array(price_history[-lookback_periods:])
            
            # Find local minima (support) and maxima (resistance)
            support_levels = []
            resistance_levels = []
            
            # Simple peak/trough detection
            for i in range(2, len(prices) - 2):
                # Local minimum (support)
                if (prices[i] < prices[i-1] and prices[i] < prices[i+1] and
                    prices[i] < prices[i-2] and prices[i] < prices[i+2]):
                    support_levels.append(prices[i])
                
                # Local maximum (resistance)
                if (prices[i] > prices[i-1] and prices[i] > prices[i+1] and
                    prices[i] > prices[i-2] and prices[i] > prices[i+2]):
                    resistance_levels.append(prices[i])
            
            # Sort and filter levels
            support_levels = sorted(set(support_levels), reverse=True)[:5]  # Top 5 support
            resistance_levels = sorted(set(resistance_levels))[:5]  # Top 5 resistance
            
            return {
                'support_levels': [round(level, 2) for level in support_levels],
                'resistance_levels': [round(level, 2) for level in resistance_levels]
            }
            
        except Exception as e:
            logger.error(f"❌ Error calculating support/resistance for {symbol}", error=str(e))
            return {'support_levels': [], 'resistance_levels': []}
    
    async def calculate_volatility_metrics(
        self,
        symbol: str,
        price_history: List[float],
        periods: List[int] = [10, 20, 50]
    ) -> Dict[str, float]:
        """Calculate various volatility metrics."""
        try:
            if len(price_history) < max(periods):
                return {}
            
            prices = pd.Series(price_history)
            returns = prices.pct_change().dropna()
            
            volatility_metrics = {}
            
            for period in periods:
                if len(returns) >= period:
                    # Historical volatility (annualized)
                    vol = returns.rolling(window=period).std().iloc[-1] * sqrt(252) * 100
                    volatility_metrics[f'volatility_{period}d'] = round(vol, 2)
                    
                    # Volatility rank (percentile of current vol vs historical)
                    vol_series = returns.rolling(window=period).std() * sqrt(252) * 100
                    current_vol = vol_series.iloc[-1]
                    vol_rank = (vol_series < current_vol).sum() / len(vol_series) * 100
                    volatility_metrics[f'vol_rank_{period}d'] = round(vol_rank, 1)
            
            # Parkinson volatility (high-low based) - simplified
            # In production, you'd use actual high/low data
            price_range = prices.rolling(window=20).max() - prices.rolling(window=20).min()
            parkinson_vol = (price_range / prices).rolling(window=20).mean() * 100
            volatility_metrics['parkinson_volatility'] = round(parkinson_vol.iloc[-1], 2)
            
            return volatility_metrics
            
        except Exception as e:
            logger.error(f"❌ Error calculating volatility metrics for {symbol}", error=str(e))
            return {}
    
    def get_indicator_summary(self, indicators: Dict[str, float]) -> Dict[str, str]:
        """Get human-readable summary of indicators."""
        try:
            summary = {}

            # RSI interpretation
            rsi = indicators.get('rsi', 50)
            if rsi > 70:
                summary['rsi'] = 'Overbought'
            elif rsi < 30:
                summary['rsi'] = 'Oversold'
            else:
                summary['rsi'] = 'Neutral'

            # MACD interpretation
            macd = indicators.get('macd', 0)
            macd_signal = indicators.get('macd_signal', 0)
            if macd > macd_signal:
                summary['macd'] = 'Bullish'
            else:
                summary['macd'] = 'Bearish'

            # Bollinger Bands interpretation
            bb_position = indicators.get('bb_position', 0.5)
            if bb_position > 0.8:
                summary['bollinger'] = 'Near Upper Band'
            elif bb_position < 0.2:
                summary['bollinger'] = 'Near Lower Band'
            else:
                summary['bollinger'] = 'Middle Range'

            # Moving average trend
            sma_20 = indicators.get('sma_20', 0)
            sma_50 = indicators.get('sma_50', 0)
            if sma_20 > sma_50:
                summary['trend'] = 'Uptrend'
            else:
                summary['trend'] = 'Downtrend'

            return summary

        except Exception as e:
            logger.error("❌ Error generating indicator summary", error=str(e))
            return {'error': str(e)}

    async def calculate_custom_indicators(
        self,
        symbol: str,
        price_history: List[float],
        volume_history: List[int]
    ) -> Dict[str, float]:
        """Calculate custom and advanced indicators."""
        try:
            if len(price_history) < 20:
                return {}

            df = pd.DataFrame({
                'close': price_history,
                'volume': volume_history
            })

            indicators = {}

            # Money Flow Index (MFI)
            if len(df) >= 14:
                typical_price = df['close']  # Simplified
                money_flow = typical_price * df['volume']

                # Positive and negative money flow
                price_change = typical_price.diff()
                positive_mf = money_flow.where(price_change > 0, 0).rolling(14).sum()
                negative_mf = money_flow.where(price_change < 0, 0).rolling(14).sum()

                mfi = 100 - (100 / (1 + positive_mf / negative_mf.replace(0, 1)))
                indicators['mfi'] = round(mfi.iloc[-1], 2)

            # Chaikin Money Flow (CMF)
            if len(df) >= 20:
                # Simplified CMF calculation
                money_flow_volume = df['volume'] * ((df['close'] - df['close'].shift(1)) / df['close'].shift(1))
                cmf = money_flow_volume.rolling(20).sum() / df['volume'].rolling(20).sum()
                indicators['cmf'] = round(cmf.iloc[-1], 4)

            # Accumulation/Distribution Line
            if len(df) >= 2:
                price_change = df['close'].diff()
                ad_line = (price_change / df['close'].shift(1) * df['volume']).cumsum()
                indicators['ad_line'] = round(ad_line.iloc[-1], 0)

            # Price Volume Trend (PVT)
            if len(df) >= 2:
                price_change_pct = df['close'].pct_change()
                pvt = (price_change_pct * df['volume']).cumsum()
                indicators['pvt'] = round(pvt.iloc[-1], 0)

            return indicators

        except Exception as e:
            logger.error(f"❌ Error calculating custom indicators for {symbol}", error=str(e))
            return {}

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get indicator cache statistics."""
        try:
            current_time = datetime.now()
            active_cache_count = 0
            expired_cache_count = 0

            for symbol, cache_entry in self.indicator_cache.items():
                cache_time = cache_entry.get('timestamp')
                if cache_time and current_time - cache_time <= self.cache_expiry:
                    active_cache_count += 1
                else:
                    expired_cache_count += 1

            return {
                'total_cached_symbols': len(self.indicator_cache),
                'active_cache_entries': active_cache_count,
                'expired_cache_entries': expired_cache_count,
                'cache_expiry_minutes': self.cache_expiry.total_seconds() / 60,
                'cache_hit_rate': active_cache_count / max(len(self.indicator_cache), 1) * 100
            }

        except Exception as e:
            logger.error("❌ Error getting cache stats", error=str(e))
            return {'error': str(e)}
