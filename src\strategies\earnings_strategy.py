"""
Earnings Momentum Strategy
Capitalizes on post-earnings announcement momentum with systematic entry and exit rules.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import structlog
import aiohttp
from base_strategy import BaseStrategy

logger = structlog.get_logger(__name__)


class EarningsStrategy(BaseStrategy):
    """
    Earnings Momentum Strategy Implementation
    
    Strategy Logic:
    1. Monitor upcoming earnings announcements
    2. Identify stocks beating estimates by >10%
    3. Confirm positive guidance or raised outlook
    4. Enter positions 1-2 days post-earnings
    5. Hold for 5-10 trading days to capture drift
    6. Stop loss at 5% or pre-earnings price
    7. Take profit at 15% or technical resistance
    """
    
    def __init__(self, config: Dict[str, Any], schwab_client, market_analyzer, risk_manager):
        """Initialize earnings strategy."""
        super().__init__(
            config=config,
            schwab_client=schwab_client,
            market_analyzer=market_analyzer,
            risk_manager=risk_manager,
            name="EarningsStrategy"
        )
        
        # Strategy parameters
        self.earnings_surprise_threshold = config.get('earnings_surprise_threshold', 0.1)  # 10%
        self.hold_period_days = config.get('hold_period_days', 7)
        self.pre_earnings_buffer_days = config.get('pre_earnings_buffer_days', 2)
        self.min_volume_increase = config.get('min_volume_increase', 2.0)  # 2x normal volume
        self.stop_loss_pct = config.get('stop_loss_pct', 0.05)  # 5%
        self.take_profit_pct = config.get('take_profit_pct', 0.15)  # 15%
        
        # Earnings calendar cache
        self.earnings_calendar: Dict[str, Dict] = {}
        self.earnings_results: Dict[str, Dict] = {}
        self.last_calendar_update = None
        
        # Watchlist for earnings plays
        self.earnings_watchlist = [
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'NVDA', 'META',
            'NFLX', 'AMD', 'CRM', 'ADBE', 'PYPL', 'INTC', 'CSCO',
            'ORCL', 'UBER', 'LYFT', 'SHOP', 'SQ', 'ROKU', 'ZM'
        ]
        
        logger.info(
            "✅ Earnings strategy initialized",
            surprise_threshold=self.earnings_surprise_threshold,
            hold_period=self.hold_period_days,
            watchlist_size=len(self.earnings_watchlist)
        )
    
    async def generate_signals(self) -> List[Dict[str, Any]]:
        """Generate earnings momentum signals."""
        if not self.is_enabled:
            return []
        
        signals = []
        
        try:
            # Update earnings calendar if needed
            await self._update_earnings_calendar()
            
            # Check for recent earnings beats
            recent_earnings = await self._get_recent_earnings_results()
            
            # Analyze each earnings result
            for symbol, earnings_data in recent_earnings.items():
                try:
                    signal = await self._analyze_earnings_opportunity(symbol, earnings_data)
                    if signal:
                        signals.append(signal)
                        await self.record_signal(signal)
                        
                except Exception as e:
                    logger.error(f"❌ Error analyzing earnings for {symbol}", error=str(e))
                    continue
            
            logger.info(f"🎯 Generated {len(signals)} earnings signals")
            return signals
            
        except Exception as e:
            logger.error("❌ Error generating earnings signals", error=str(e), exc_info=True)
            return []
    
    async def _update_earnings_calendar(self) -> None:
        """Update earnings calendar from external API."""
        try:
            # Check if update is needed (daily update)
            if (self.last_calendar_update and 
                datetime.now() - self.last_calendar_update < timedelta(hours=6)):
                return
            
            # In production, you'd use a real earnings API like Alpha Vantage, Finnhub, etc.
            # For demo purposes, we'll simulate earnings data
            await self._simulate_earnings_calendar()
            
            self.last_calendar_update = datetime.now()
            logger.info("✅ Earnings calendar updated")
            
        except Exception as e:
            logger.error("❌ Error updating earnings calendar", error=str(e))
    
    async def _simulate_earnings_calendar(self) -> None:
        """Simulate earnings calendar data for demonstration."""
        # This would be replaced with real API calls in production
        current_date = datetime.now()
        
        # Simulate some recent earnings
        simulated_earnings = {
            'AAPL': {
                'earnings_date': current_date - timedelta(days=1),
                'actual_eps': 1.25,
                'estimated_eps': 1.15,
                'surprise_pct': 0.087,  # 8.7% beat
                'revenue_actual': 89.5e9,
                'revenue_estimate': 87.2e9,
                'guidance': 'raised'
            },
            'MSFT': {
                'earnings_date': current_date - timedelta(days=2),
                'actual_eps': 2.45,
                'estimated_eps': 2.30,
                'surprise_pct': 0.065,  # 6.5% beat
                'revenue_actual': 52.7e9,
                'revenue_estimate': 51.8e9,
                'guidance': 'maintained'
            }
        }
        
        self.earnings_results.update(simulated_earnings)
    
    async def _get_recent_earnings_results(self) -> Dict[str, Dict]:
        """Get earnings results from the last 3 days."""
        recent_results = {}
        cutoff_date = datetime.now() - timedelta(days=3)
        
        for symbol, data in self.earnings_results.items():
            earnings_date = data.get('earnings_date')
            if earnings_date and earnings_date >= cutoff_date:
                # Check if we haven't already traded this earnings
                if not self._already_traded_earnings(symbol, earnings_date):
                    recent_results[symbol] = data
        
        return recent_results
    
    async def _analyze_earnings_opportunity(
        self, 
        symbol: str, 
        earnings_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Analyze individual earnings opportunity."""
        try:
            # Check earnings surprise threshold
            surprise_pct = earnings_data.get('surprise_pct', 0)
            if surprise_pct < self.earnings_surprise_threshold:
                logger.debug(f"📊 {symbol} earnings surprise too low: {surprise_pct:.1%}")
                return None
            
            # Get current market data
            market_data = await self.get_market_data([symbol])
            if not market_data or symbol not in market_data:
                return None
            
            quote = market_data[symbol]
            current_price = quote.get('last', 0)
            current_volume = quote.get('volume', 0)
            
            if not current_price:
                return None
            
            # Check volume confirmation
            avg_volume = quote.get('avgVolume', current_volume)
            volume_ratio = current_volume / max(avg_volume, 1)
            
            if volume_ratio < self.min_volume_increase:
                logger.debug(f"📊 {symbol} volume too low: {volume_ratio:.1f}x")
                return None
            
            # Calculate technical indicators
            indicators = await self.calculate_technical_indicators(symbol)
            
            # Calculate earnings momentum score
            momentum_score = await self._calculate_earnings_score(
                symbol, earnings_data, current_price, volume_ratio, indicators
            )
            
            if momentum_score >= 0.7:  # 70% confidence threshold
                return await self._create_earnings_signal(
                    symbol, current_price, earnings_data, momentum_score
                )
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error analyzing earnings opportunity for {symbol}", error=str(e))
            return None
    
    async def _calculate_earnings_score(
        self,
        symbol: str,
        earnings_data: Dict[str, Any],
        current_price: float,
        volume_ratio: float,
        indicators: Dict[str, float]
    ) -> float:
        """Calculate earnings momentum score."""
        score_components = []
        
        try:
            # 1. Earnings surprise score (30% weight)
            surprise_pct = earnings_data.get('surprise_pct', 0)
            surprise_score = min(1.0, surprise_pct / 0.2)  # Max at 20% surprise
            score_components.append(('earnings_surprise', surprise_score, 0.30))
            
            # 2. Revenue beat score (20% weight)
            revenue_actual = earnings_data.get('revenue_actual', 0)
            revenue_estimate = earnings_data.get('revenue_estimate', 1)
            revenue_beat = (revenue_actual - revenue_estimate) / revenue_estimate
            revenue_score = min(1.0, max(0, revenue_beat / 0.1))  # Max at 10% revenue beat
            score_components.append(('revenue_beat', revenue_score, 0.20))
            
            # 3. Guidance score (20% weight)
            guidance = earnings_data.get('guidance', 'maintained')
            guidance_scores = {'raised': 1.0, 'maintained': 0.5, 'lowered': 0.0}
            guidance_score = guidance_scores.get(guidance, 0.5)
            score_components.append(('guidance', guidance_score, 0.20))
            
            # 4. Volume confirmation score (15% weight)
            volume_score = min(1.0, volume_ratio / 5.0)  # Max at 5x volume
            score_components.append(('volume', volume_score, 0.15))
            
            # 5. Technical momentum score (15% weight)
            rsi = indicators.get('rsi', 50)
            macd = indicators.get('macd', 0)
            
            # Prefer RSI between 50-70 (momentum but not overbought)
            if 50 <= rsi <= 70:
                rsi_score = 1.0
            elif rsi > 70:
                rsi_score = 0.7  # Overbought but still positive
            else:
                rsi_score = 0.3  # Weak momentum
            
            macd_score = 1.0 if macd > 0 else 0.3
            technical_score = (rsi_score + macd_score) / 2
            score_components.append(('technical', technical_score, 0.15))
            
            # Calculate weighted score
            total_score = sum(score * weight for _, score, weight in score_components)
            
            logger.debug(
                f"📊 Earnings score calculated",
                symbol=symbol,
                total_score=total_score,
                components=score_components
            )
            
            return total_score
            
        except Exception as e:
            logger.error(f"❌ Error calculating earnings score for {symbol}", error=str(e))
            return 0.0
    
    async def _create_earnings_signal(
        self,
        symbol: str,
        current_price: float,
        earnings_data: Dict[str, Any],
        confidence: float
    ) -> Dict[str, Any]:
        """Create an earnings momentum buy signal."""
        
        # Calculate stop loss (5% or pre-earnings price)
        earnings_date = earnings_data.get('earnings_date')
        pre_earnings_price = await self._get_pre_earnings_price(symbol, earnings_date)
        
        stop_loss_price = min(
            current_price * (1 - self.stop_loss_pct),
            pre_earnings_price * 0.98 if pre_earnings_price else current_price * 0.95
        )
        
        # Calculate take profit (15% target)
        take_profit_price = current_price * (1 + self.take_profit_pct)
        
        # Calculate expected hold period
        entry_date = datetime.now()
        expected_exit_date = entry_date + timedelta(days=self.hold_period_days)
        
        signal = {
            'symbol': symbol,
            'side': 'BUY',
            'order_type': 'LIMIT',
            'entry_price': current_price,
            'stop_loss': stop_loss_price,
            'take_profit': take_profit_price,
            'confidence': confidence,
            'reason': f'Earnings momentum - {earnings_data.get("surprise_pct", 0):.1%} EPS beat',
            'expected_hold_days': self.hold_period_days,
            'expected_exit_date': expected_exit_date,
            'strategy_data': {
                'earnings_date': earnings_data.get('earnings_date'),
                'eps_surprise': earnings_data.get('surprise_pct'),
                'revenue_beat': (earnings_data.get('revenue_actual', 0) - 
                               earnings_data.get('revenue_estimate', 0)) / 
                               max(earnings_data.get('revenue_estimate', 1), 1),
                'guidance': earnings_data.get('guidance'),
                'entry_reason': 'post_earnings_momentum'
            }
        }
        
        logger.info(
            f"🎯 Earnings signal generated",
            symbol=symbol,
            price=current_price,
            confidence=confidence,
            eps_surprise=earnings_data.get('surprise_pct', 0),
            guidance=earnings_data.get('guidance')
        )
        
        return signal
    
    async def _get_pre_earnings_price(self, symbol: str, earnings_date: datetime) -> Optional[float]:
        """Get stock price before earnings announcement."""
        try:
            # In production, you'd fetch historical price data
            # For demo, we'll estimate based on current price and typical earnings moves
            market_data = await self.get_market_data([symbol])
            if not market_data or symbol not in market_data:
                return None
            
            current_price = market_data[symbol].get('last', 0)
            # Estimate pre-earnings price (assuming 5% average earnings move)
            estimated_pre_earnings = current_price / 1.05
            
            return estimated_pre_earnings
            
        except Exception as e:
            logger.error(f"❌ Error getting pre-earnings price for {symbol}", error=str(e))
            return None
    
    def _already_traded_earnings(self, symbol: str, earnings_date: datetime) -> bool:
        """Check if we've already traded this earnings announcement."""
        # Check signal history for recent earnings trades
        for signal in self.signal_history[-20:]:  # Check last 20 signals
            if (signal.get('symbol') == symbol and 
                signal.get('strategy_data', {}).get('earnings_date') == earnings_date):
                return True
        
        return False
    
    async def should_exit_position(self, symbol: str, position_data: Dict) -> Tuple[bool, str]:
        """Determine if an earnings position should be exited."""
        try:
            # Get current market data
            market_data = await self.get_market_data([symbol])
            if not market_data or symbol not in market_data:
                return False, "No market data available"
            
            current_price = market_data[symbol].get('last', 0)
            entry_price = position_data.get('avg_cost', 0)
            
            if not current_price or not entry_price:
                return False, "Invalid price data"
            
            # Check stop loss
            stop_loss = position_data.get('stop_loss')
            if stop_loss and current_price <= stop_loss:
                return True, f"Stop loss triggered at {current_price}"
            
            # Check take profit
            take_profit = position_data.get('take_profit')
            if take_profit and current_price >= take_profit:
                return True, f"Take profit reached at {current_price}"
            
            # Check hold period
            entry_date = position_data.get('entry_date')
            if entry_date:
                days_held = (datetime.now() - entry_date).days
                if days_held >= self.hold_period_days:
                    return True, f"Maximum hold period reached ({days_held} days)"
            
            # Check for momentum reversal
            indicators = await self.calculate_technical_indicators(symbol)
            rsi = indicators.get('rsi', 50)
            
            # Exit if RSI becomes overbought (>80) or momentum weakens significantly
            if rsi > 80:
                return True, f"Overbought condition - RSI: {rsi:.1f}"
            
            # Check for negative news or guidance revision
            # In production, you'd monitor news feeds and analyst updates
            
            return False, "Hold position"
            
        except Exception as e:
            logger.error(f"❌ Error checking exit condition for {symbol}", error=str(e))
            return False, f"Error checking exit: {str(e)}"
    
    async def _strategy_specific_validation(self, signal: Dict[str, Any]) -> Tuple[bool, str]:
        """Earnings strategy specific validation."""
        try:
            # Check if we already have a position in this symbol
            if signal['symbol'] in self.active_positions:
                return False, "Already have position in this symbol"
            
            # Validate confidence threshold
            if signal.get('confidence', 0) < 0.6:
                return False, f"Confidence too low: {signal.get('confidence', 0):.2f}"
            
            # Check if earnings data is recent (within 3 days)
            strategy_data = signal.get('strategy_data', {})
            earnings_date = strategy_data.get('earnings_date')
            
            if earnings_date:
                days_since_earnings = (datetime.now() - earnings_date).days
                if days_since_earnings > 3:
                    return False, f"Earnings too old: {days_since_earnings} days"
            
            # Validate EPS surprise threshold
            eps_surprise = strategy_data.get('eps_surprise', 0)
            if eps_surprise < self.earnings_surprise_threshold:
                return False, f"EPS surprise too low: {eps_surprise:.1%}"
            
            return True, "Earnings validation passed"
            
        except Exception as e:
            logger.error("❌ Error in earnings validation", error=str(e))
            return False, f"Validation error: {str(e)}"
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """Get strategy information and current state."""
        return {
            'name': self.name,
            'type': 'earnings_momentum',
            'enabled': self.is_enabled,
            'parameters': {
                'earnings_surprise_threshold': self.earnings_surprise_threshold,
                'hold_period_days': self.hold_period_days,
                'min_volume_increase': self.min_volume_increase,
                'stop_loss_pct': self.stop_loss_pct,
                'take_profit_pct': self.take_profit_pct
            },
            'watchlist_size': len(self.earnings_watchlist),
            'active_positions': len(self.active_positions),
            'earnings_calendar_size': len(self.earnings_calendar),
            'recent_earnings_count': len(self.earnings_results),
            'performance': self.get_performance_metrics(),
            'last_calendar_update': self.last_calendar_update
        }
