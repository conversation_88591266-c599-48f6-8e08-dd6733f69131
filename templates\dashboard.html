<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Schwab Trading Bot Dashboard</title>
    
    <!-- Meta tags for SEO -->
    <meta name="description" content="Real-time trading dashboard for Schwab Telegram Trading Bot with portfolio analytics, performance metrics, and risk monitoring.">
    <meta name="keywords" content="schwab trading bot, algorithmic trading, telegram bot, portfolio dashboard, trading analytics">
    <meta name="author" content="HectorTa1989">
    
    <!-- Open Graph tags -->
    <meta property="og:title" content="Schwab Trading Bot Dashboard">
    <meta property="og:description" content="Professional trading bot dashboard with real-time analytics">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://schwab-trading-bot.com/dashboard">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    
    <!-- CSS Framework -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Chart.js for analytics -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom styles -->
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        .metric-positive {
            color: #10b981;
        }
        
        .metric-negative {
            color: #ef4444;
        }
        
        .status-healthy {
            color: #10b981;
        }
        
        .status-warning {
            color: #f59e0b;
        }
        
        .status-critical {
            color: #ef4444;
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="container mx-auto px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <i class="fas fa-chart-line text-2xl"></i>
                    <h1 class="text-2xl font-bold">Schwab Trading Bot</h1>
                    <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full text-sm" id="status-badge">
                        <i class="fas fa-circle text-green-400"></i> Online
                    </span>
                </div>
                
                <div class="flex items-center space-x-4">
                    <span class="text-sm opacity-90" id="last-update">
                        Last Update: <span id="update-time">--:--</span>
                    </span>
                    <button onclick="refreshDashboard()" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-all">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Dashboard -->
    <main class="container mx-auto px-6 py-8">
        <!-- Key Metrics Row -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Portfolio Value -->
            <div class="card p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm">Portfolio Value</p>
                        <p class="text-2xl font-bold" id="portfolio-value">$--</p>
                        <p class="text-sm" id="portfolio-change">
                            <span class="metric-positive">+$-- (+---%)</span>
                        </p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-full">
                        <i class="fas fa-wallet text-blue-600 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Day P&L -->
            <div class="card p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm">Day P&L</p>
                        <p class="text-2xl font-bold" id="day-pnl">$--</p>
                        <p class="text-sm" id="day-pnl-pct">---%</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-full">
                        <i class="fas fa-chart-line text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Active Positions -->
            <div class="card p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm">Active Positions</p>
                        <p class="text-2xl font-bold" id="position-count">--</p>
                        <p class="text-sm text-gray-500" id="position-value">$-- invested</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-full">
                        <i class="fas fa-layer-group text-purple-600 text-xl"></i>
                    </div>
                </div>
            </div>
            
            <!-- Win Rate -->
            <div class="card p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-600 text-sm">Win Rate (30d)</p>
                        <p class="text-2xl font-bold" id="win-rate">--%</p>
                        <p class="text-sm text-gray-500" id="total-trades">-- trades</p>
                    </div>
                    <div class="bg-yellow-100 p-3 rounded-full">
                        <i class="fas fa-target text-yellow-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts and Analytics Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Portfolio Performance Chart -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold mb-4">Portfolio Performance</h3>
                <canvas id="performance-chart" width="400" height="200"></canvas>
            </div>
            
            <!-- Strategy Performance -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold mb-4">Strategy Performance</h3>
                <canvas id="strategy-chart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Positions and Trades Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Current Positions -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold mb-4">Current Positions</h3>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="border-b">
                                <th class="text-left py-2">Symbol</th>
                                <th class="text-right py-2">Qty</th>
                                <th class="text-right py-2">Value</th>
                                <th class="text-right py-2">P&L</th>
                            </tr>
                        </thead>
                        <tbody id="positions-table">
                            <tr>
                                <td colspan="4" class="text-center py-4 text-gray-500">Loading positions...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Recent Trades -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold mb-4">Recent Trades</h3>
                <div class="overflow-x-auto">
                    <table class="w-full text-sm">
                        <thead>
                            <tr class="border-b">
                                <th class="text-left py-2">Symbol</th>
                                <th class="text-left py-2">Side</th>
                                <th class="text-right py-2">Price</th>
                                <th class="text-right py-2">Time</th>
                            </tr>
                        </thead>
                        <tbody id="trades-table">
                            <tr>
                                <td colspan="4" class="text-center py-4 text-gray-500">Loading trades...</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- System Status Row -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- System Health -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold mb-4">System Health</h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span>Database</span>
                        <span class="status-healthy" id="db-status">
                            <i class="fas fa-check-circle"></i> Healthy
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span>Schwab API</span>
                        <span class="status-healthy" id="api-status">
                            <i class="fas fa-check-circle"></i> Connected
                        </span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span>Telegram</span>
                        <span class="status-healthy" id="telegram-status">
                            <i class="fas fa-check-circle"></i> Active
                        </span>
                    </div>
                </div>
            </div>
            
            <!-- Market Conditions -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold mb-4">Market Conditions</h3>
                <div class="space-y-3">
                    <div class="flex justify-between items-center">
                        <span>Sentiment</span>
                        <span id="market-sentiment">Neutral</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span>VIX Level</span>
                        <span id="vix-level">--</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span>Leading Sector</span>
                        <span id="leading-sector">--</span>
                    </div>
                </div>
            </div>
            
            <!-- Active Strategies -->
            <div class="card p-6">
                <h3 class="text-lg font-semibold mb-4">Active Strategies</h3>
                <div class="space-y-3" id="strategies-list">
                    <div class="flex justify-between items-center">
                        <span>Loading strategies...</span>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- JavaScript -->
    <script>
        // Dashboard state
        let dashboardData = {};
        let charts = {};
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeDashboard();
            startAutoRefresh();
        });
        
        async function initializeDashboard() {
            console.log('🚀 Initializing dashboard...');
            await refreshDashboard();
            initializeCharts();
        }
        
        async function refreshDashboard() {
            try {
                document.body.classList.add('loading');
                
                // Fetch data from API endpoints
                const [portfolioData, tradesData, metricsData, healthData] = await Promise.all([
                    fetchAPI('/api/portfolio'),
                    fetchAPI('/api/trades?limit=10'),
                    fetchAPI('/api/metrics?range=24h'),
                    fetchAPI('/api/health')
                ]);
                
                // Update dashboard
                updatePortfolioMetrics(portfolioData);
                updatePositionsTable(portfolioData.positions || []);
                updateTradesTable(tradesData.trades || []);
                updateSystemHealth(healthData);
                updateMarketConditions(metricsData);
                
                // Update timestamp
                document.getElementById('update-time').textContent = new Date().toLocaleTimeString();
                
                console.log('✅ Dashboard refreshed successfully');
                
            } catch (error) {
                console.error('❌ Error refreshing dashboard:', error);
                showError('Failed to refresh dashboard data');
            } finally {
                document.body.classList.remove('loading');
            }
        }
        
        async function fetchAPI(endpoint) {
            const response = await fetch(endpoint);
            if (!response.ok) {
                throw new Error(`API request failed: ${response.status}`);
            }
            return response.json();
        }
        
        function updatePortfolioMetrics(data) {
            const summary = data.account_summary || {};
            
            // Portfolio value
            document.getElementById('portfolio-value').textContent = 
                formatCurrency(summary.total_value || 0);
            
            // Day P&L
            const dayPnl = summary.day_pnl || 0;
            const dayPnlElement = document.getElementById('day-pnl');
            dayPnlElement.textContent = formatCurrency(dayPnl);
            dayPnlElement.className = dayPnl >= 0 ? 'text-2xl font-bold metric-positive' : 'text-2xl font-bold metric-negative';
            
            // Position count
            document.getElementById('position-count').textContent = (data.positions || []).length;
            
            // Win rate
            const performance = data.performance || {};
            document.getElementById('win-rate').textContent = `${performance.win_rate || 0}%`;
            document.getElementById('total-trades').textContent = `${performance.total_trades || 0} trades`;
        }
        
        function updatePositionsTable(positions) {
            const tbody = document.getElementById('positions-table');
            
            if (!positions || positions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" class="text-center py-4 text-gray-500">No positions</td></tr>';
                return;
            }
            
            tbody.innerHTML = positions.map(position => `
                <tr class="border-b hover:bg-gray-50">
                    <td class="py-2 font-medium">${position.symbol}</td>
                    <td class="py-2 text-right">${position.quantity}</td>
                    <td class="py-2 text-right">${formatCurrency(position.market_value)}</td>
                    <td class="py-2 text-right ${position.unrealized_pnl >= 0 ? 'metric-positive' : 'metric-negative'}">
                        ${formatCurrency(position.unrealized_pnl)} (${position.unrealized_pnl_percent?.toFixed(1)}%)
                    </td>
                </tr>
            `).join('');
        }
        
        function updateTradesTable(trades) {
            const tbody = document.getElementById('trades-table');
            
            if (!trades || trades.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" class="text-center py-4 text-gray-500">No recent trades</td></tr>';
                return;
            }
            
            tbody.innerHTML = trades.slice(0, 5).map(trade => `
                <tr class="border-b hover:bg-gray-50">
                    <td class="py-2 font-medium">${trade.symbol}</td>
                    <td class="py-2">
                        <span class="px-2 py-1 rounded text-xs ${trade.side === 'BUY' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${trade.side}
                        </span>
                    </td>
                    <td class="py-2 text-right">${formatCurrency(trade.price)}</td>
                    <td class="py-2 text-right text-sm text-gray-500">
                        ${new Date(trade.timestamp).toLocaleTimeString()}
                    </td>
                </tr>
            `).join('');
        }
        
        function updateSystemHealth(healthData) {
            const components = healthData.components || {};
            
            // Database status
            updateStatusElement('db-status', components.database?.status || 'unknown');
            
            // API status
            updateStatusElement('api-status', components.external_apis?.status || 'unknown');
            
            // Telegram status
            updateStatusElement('telegram-status', 'healthy'); // Assume healthy if dashboard loads
        }
        
        function updateStatusElement(elementId, status) {
            const element = document.getElementById(elementId);
            const statusConfig = {
                'healthy': { icon: 'check-circle', class: 'status-healthy', text: 'Healthy' },
                'degraded': { icon: 'exclamation-triangle', class: 'status-warning', text: 'Degraded' },
                'critical': { icon: 'times-circle', class: 'status-critical', text: 'Critical' },
                'unknown': { icon: 'question-circle', class: 'text-gray-500', text: 'Unknown' }
            };
            
            const config = statusConfig[status] || statusConfig['unknown'];
            element.innerHTML = `<i class="fas fa-${config.icon}"></i> ${config.text}`;
            element.className = config.class;
        }
        
        function updateMarketConditions(metricsData) {
            // This would be populated with real market data
            document.getElementById('market-sentiment').textContent = 'Moderately Bullish';
            document.getElementById('vix-level').textContent = '18.5';
            document.getElementById('leading-sector').textContent = 'Technology';
        }
        
        function initializeCharts() {
            // Performance chart
            const performanceCtx = document.getElementById('performance-chart').getContext('2d');
            charts.performance = new Chart(performanceCtx, {
                type: 'line',
                data: {
                    labels: ['1d', '7d', '14d', '21d', '30d'],
                    datasets: [{
                        label: 'Portfolio Value',
                        data: [100000, 102000, 101500, 103000, 105000],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: false,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    }
                }
            });
            
            // Strategy performance chart
            const strategyCtx = document.getElementById('strategy-chart').getContext('2d');
            charts.strategy = new Chart(strategyCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Momentum', 'Earnings', 'Institutional'],
                    datasets: [{
                        data: [45, 30, 25],
                        backgroundColor: ['#10b981', '#f59e0b', '#8b5cf6']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
        
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
                minimumFractionDigits: 2
            }).format(amount);
        }
        
        function showError(message) {
            // Simple error notification
            const errorDiv = document.createElement('div');
            errorDiv.className = 'fixed top-4 right-4 bg-red-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            errorDiv.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${message}`;
            
            document.body.appendChild(errorDiv);
            
            setTimeout(() => {
                errorDiv.remove();
            }, 5000);
        }
        
        function startAutoRefresh() {
            // Refresh every 30 seconds
            setInterval(refreshDashboard, 30000);
        }
        
        // Global functions
        window.refreshDashboard = refreshDashboard;
    </script>
</body>
</html>
