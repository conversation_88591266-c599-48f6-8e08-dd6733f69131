"""
Test Suite for Schwab API Client
Comprehensive testing with mocks, rate limiting, and error scenarios.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import pytest
import asyncio
import json
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
import aiohttp
from aioresponses import aioresponses

# Import test configuration
from . import TEST_CONFIG

# Import the module to test
from src.schwab_client import SchwabClient


class TestSchwabClient:
    """Test suite for Schwab API client."""
    
    @pytest.fixture
    async def schwab_client(self):
        """Create a test Schwab client."""
        client = SchwabClient(
            client_id=TEST_CONFIG['schwab']['client_id'],
            client_secret=TEST_CONFIG['schwab']['client_secret'],
            redirect_uri=TEST_CONFIG['schwab']['redirect_uri'],
            account_number=TEST_CONFIG['schwab']['account_number']
        )
        
        # Mock the session to avoid actual HTTP calls
        client.session = AsyncMock()
        
        yield client
        
        # Cleanup
        if client.session:
            await client.session.close()
    
    @pytest.mark.asyncio
    async def test_initialization(self, schwab_client):
        """Test client initialization."""
        assert schwab_client.client_id == TEST_CONFIG['schwab']['client_id']
        assert schwab_client.client_secret == TEST_CONFIG['schwab']['client_secret']
        assert schwab_client.redirect_uri == TEST_CONFIG['schwab']['redirect_uri']
        assert schwab_client.account_number == TEST_CONFIG['schwab']['account_number']
        assert schwab_client.access_token is None
        assert schwab_client.refresh_token is None
    
    @pytest.mark.asyncio
    async def test_token_exchange(self, schwab_client):
        """Test OAuth token exchange."""
        # Mock successful token response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {
            'access_token': 'test_access_token',
            'refresh_token': 'test_refresh_token',
            'expires_in': 3600,
            'token_type': 'Bearer'
        }
        
        schwab_client.session.post.return_value.__aenter__.return_value = mock_response
        
        # Test token exchange
        await schwab_client._exchange_code_for_tokens('test_auth_code')
        
        assert schwab_client.access_token == 'test_access_token'
        assert schwab_client.refresh_token == 'test_refresh_token'
        assert schwab_client.token_expires_at is not None
    
    @pytest.mark.asyncio
    async def test_token_refresh(self, schwab_client):
        """Test token refresh functionality."""
        # Set initial tokens
        schwab_client.refresh_token = 'test_refresh_token'
        schwab_client.token_expires_at = datetime.now() - timedelta(minutes=5)  # Expired
        
        # Mock successful refresh response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {
            'access_token': 'new_access_token',
            'refresh_token': 'new_refresh_token',
            'expires_in': 3600
        }
        
        schwab_client.session.post.return_value.__aenter__.return_value = mock_response
        
        # Test token refresh
        await schwab_client._refresh_access_token()
        
        assert schwab_client.access_token == 'new_access_token'
        assert schwab_client.refresh_token == 'new_refresh_token'
    
    @pytest.mark.asyncio
    async def test_rate_limiting(self, schwab_client):
        """Test rate limiting functionality."""
        # Set valid token
        schwab_client.access_token = 'test_token'
        schwab_client.token_expires_at = datetime.now() + timedelta(hours=1)
        
        # Mock successful API response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {'test': 'data'}
        
        schwab_client.session.request.return_value.__aenter__.return_value = mock_response
        
        # Make multiple requests quickly
        start_time = datetime.now()
        
        for i in range(5):
            await schwab_client._make_request('GET', '/test/endpoint')
        
        end_time = datetime.now()
        
        # Should have taken some time due to rate limiting
        elapsed_time = (end_time - start_time).total_seconds()
        
        # Verify rate limiter was used
        assert schwab_client.rate_limiter.total_requests >= 5
    
    @pytest.mark.asyncio
    async def test_api_error_handling(self, schwab_client):
        """Test API error handling."""
        # Set valid token
        schwab_client.access_token = 'test_token'
        schwab_client.token_expires_at = datetime.now() + timedelta(hours=1)
        
        # Mock error response
        mock_response = AsyncMock()
        mock_response.status = 400
        mock_response.text.return_value = 'Bad Request'
        
        schwab_client.session.request.return_value.__aenter__.return_value = mock_response
        
        # Test error handling
        with pytest.raises(Exception) as exc_info:
            await schwab_client._make_request('GET', '/test/endpoint')
        
        assert 'API request failed' in str(exc_info.value)
        assert schwab_client.request_stats['failed_requests'] > 0
    
    @pytest.mark.asyncio
    async def test_rate_limit_handling(self, schwab_client):
        """Test 429 rate limit response handling."""
        # Set valid token
        schwab_client.access_token = 'test_token'
        schwab_client.token_expires_at = datetime.now() + timedelta(hours=1)
        
        # Mock rate limit response
        mock_response = AsyncMock()
        mock_response.status = 429
        mock_response.text.return_value = 'Too Many Requests'
        
        schwab_client.session.request.return_value.__aenter__.return_value = mock_response
        
        # Test rate limit handling
        with pytest.raises(Exception) as exc_info:
            await schwab_client._make_request('GET', '/test/endpoint')
        
        assert 'Rate limited' in str(exc_info.value)
        assert schwab_client.request_stats['rate_limited_requests'] > 0
    
    @pytest.mark.asyncio
    async def test_get_account_info(self, schwab_client):
        """Test account info retrieval."""
        # Set valid token
        schwab_client.access_token = 'test_token'
        schwab_client.token_expires_at = datetime.now() + timedelta(hours=1)
        
        # Mock account info response
        mock_account_data = {
            'totalValue': 100000.00,
            'cashAvailable': 25000.00,
            'buyingPower': 50000.00,
            'longMarketValue': 75000.00,
            'type': 'MARGIN'
        }
        
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = mock_account_data
        
        schwab_client.session.request.return_value.__aenter__.return_value = mock_response
        
        # Test account info retrieval
        account_info = await schwab_client.get_account_info()
        
        assert account_info == mock_account_data
        assert account_info['totalValue'] == 100000.00
    
    @pytest.mark.asyncio
    async def test_place_order(self, schwab_client):
        """Test order placement."""
        # Set valid token
        schwab_client.access_token = 'test_token'
        schwab_client.token_expires_at = datetime.now() + timedelta(hours=1)
        
        # Mock successful order response
        mock_response = AsyncMock()
        mock_response.status = 201
        mock_response.json.return_value = {'orderId': 'test_order_123'}
        
        schwab_client.session.request.return_value.__aenter__.return_value = mock_response
        
        # Test order placement
        order_result = await schwab_client.place_order(
            symbol='AAPL',
            quantity=10,
            order_type='LIMIT',
            side='BUY',
            price=150.00
        )
        
        assert order_result['success'] is True
        assert order_result['order_id'] == 'test_order_123'
    
    @pytest.mark.asyncio
    async def test_get_quotes(self, schwab_client):
        """Test quote retrieval."""
        # Set valid token
        schwab_client.access_token = 'test_token'
        schwab_client.token_expires_at = datetime.now() + timedelta(hours=1)
        
        # Mock quotes response
        mock_quotes_data = {
            'AAPL': {
                'last': 150.25,
                'bid': 150.20,
                'ask': 150.30,
                'volume': 45000000,
                'change': 2.15,
                'changePercent': 1.45
            }
        }
        
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = mock_quotes_data
        
        schwab_client.session.request.return_value.__aenter__.return_value = mock_response
        
        # Test quote retrieval
        quotes = await schwab_client.get_quotes(['AAPL'])
        
        assert quotes == mock_quotes_data
        assert quotes['AAPL']['last'] == 150.25
    
    @pytest.mark.asyncio
    async def test_token_persistence(self, schwab_client):
        """Test token saving and loading."""
        # Set test tokens
        schwab_client.access_token = 'test_access_token'
        schwab_client.refresh_token = 'test_refresh_token'
        schwab_client.token_expires_at = datetime.now() + timedelta(hours=1)
        
        # Test saving tokens
        await schwab_client._save_tokens()
        
        # Create new client and test loading
        new_client = SchwabClient(
            client_id=TEST_CONFIG['schwab']['client_id'],
            client_secret=TEST_CONFIG['schwab']['client_secret'],
            redirect_uri=TEST_CONFIG['schwab']['redirect_uri'],
            account_number=TEST_CONFIG['schwab']['account_number']
        )
        
        await new_client._load_tokens()
        
        assert new_client.access_token == 'test_access_token'
        assert new_client.refresh_token == 'test_refresh_token'
    
    @pytest.mark.asyncio
    async def test_request_statistics(self, schwab_client):
        """Test request statistics tracking."""
        initial_stats = schwab_client.get_request_stats()
        
        assert initial_stats['total_requests'] == 0
        assert initial_stats['successful_requests'] == 0
        assert initial_stats['failed_requests'] == 0
        
        # Set valid token
        schwab_client.access_token = 'test_token'
        schwab_client.token_expires_at = datetime.now() + timedelta(hours=1)
        
        # Mock successful response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.json.return_value = {'test': 'data'}
        
        schwab_client.session.request.return_value.__aenter__.return_value = mock_response
        
        # Make a request
        await schwab_client._make_request('GET', '/test')
        
        # Check updated stats
        updated_stats = schwab_client.get_request_stats()
        assert updated_stats['total_requests'] == 1
        assert updated_stats['successful_requests'] == 1


@pytest.mark.asyncio
async def test_schwab_client_integration():
    """Integration test for Schwab client (requires actual credentials)."""
    # Skip if no real credentials available
    if not os.getenv('SCHWAB_CLIENT_ID'):
        pytest.skip("No Schwab credentials available for integration test")
    
    client = SchwabClient(
        client_id=os.getenv('SCHWAB_CLIENT_ID'),
        client_secret=os.getenv('SCHWAB_CLIENT_SECRET'),
        redirect_uri=os.getenv('SCHWAB_REDIRECT_URI'),
        account_number=os.getenv('SCHWAB_ACCOUNT_NUMBER')
    )
    
    try:
        await client.initialize()
        
        # Test basic functionality (if tokens are available)
        if client.access_token:
            account_info = await client.get_account_info()
            assert 'totalValue' in account_info
            
            quotes = await client.get_quotes(['SPY'])
            assert 'SPY' in quotes
            
    except Exception as e:
        # Integration tests may fail without proper setup
        pytest.skip(f"Integration test failed: {e}")
    
    finally:
        await client.close()


if __name__ == '__main__':
    # Run tests
    pytest.main([__file__, '-v'])
