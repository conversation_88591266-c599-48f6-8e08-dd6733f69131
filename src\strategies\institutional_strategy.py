"""
Institutional Following Strategy
Tracks and follows institutional investor movements with smart money analysis.

Author: Hector<PERSON>a1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import structlog
import aiohttp
import json
from base_strategy import BaseStrategy

logger = structlog.get_logger(__name__)


class InstitutionalStrategy(BaseStrategy):
    """
    Institutional Following Strategy Implementation
    
    Strategy Logic:
    1. Monitor 13F filings for new institutional positions
    2. Track unusual options activity (large block trades)
    3. Identify smart money flows and insider activity
    4. Follow with 1-3 day delay to avoid front-running
    5. Position size based on institutional conviction
    6. Exit when institutional interest wanes
    """
    
    def __init__(self, config: Dict[str, Any], schwab_client, market_analyzer, risk_manager):
        """Initialize institutional following strategy."""
        super().__init__(
            config=config,
            schwab_client=schwab_client,
            market_analyzer=market_analyzer,
            risk_manager=risk_manager,
            name="InstitutionalStrategy"
        )
        
        # Strategy parameters
        self.min_position_size = config.get('min_position_size', 1000000)  # $1M minimum
        self.follow_delay_days = config.get('follow_delay_days', 2)
        self.min_ownership_pct = config.get('min_ownership_pct', 0.01)  # 1% ownership
        self.max_follow_age_days = config.get('max_follow_age_days', 30)
        
        # Data sources
        self.filing_sources = config.get('13f_filing_sources', ['SEC'])
        self.options_flow_threshold = config.get('options_flow_threshold', 500000)  # $500K
        
        # Institutional data cache
        self.institutional_positions: Dict[str, List[Dict]] = {}
        self.options_flow_data: Dict[str, List[Dict]] = {}
        self.insider_activity: Dict[str, List[Dict]] = {}
        self.last_data_update = None
        
        # Tracked institutions (major funds and hedge funds)
        self.tracked_institutions = [
            'Berkshire Hathaway', 'Vanguard Group', 'BlackRock',
            'State Street', 'Fidelity', 'Capital Group',
            'Bridgewater Associates', 'Renaissance Technologies',
            'Citadel', 'Two Sigma', 'DE Shaw', 'AQR Capital'
        ]
        
        logger.info(
            "✅ Institutional strategy initialized",
            min_position_size=self.min_position_size,
            follow_delay=self.follow_delay_days,
            tracked_institutions=len(self.tracked_institutions)
        )
    
    async def generate_signals(self) -> List[Dict[str, Any]]:
        """Generate institutional following signals."""
        if not self.is_enabled:
            return []
        
        signals = []
        
        try:
            # Update institutional data if needed
            await self._update_institutional_data()
            
            # Analyze 13F filings
            filing_signals = await self._analyze_13f_filings()
            signals.extend(filing_signals)
            
            # Analyze options flow
            options_signals = await self._analyze_options_flow()
            signals.extend(options_signals)
            
            # Analyze insider activity
            insider_signals = await self._analyze_insider_activity()
            signals.extend(insider_signals)
            
            # Remove duplicates and rank by confidence
            signals = await self._deduplicate_and_rank_signals(signals)
            
            # Record all signals
            for signal in signals:
                await self.record_signal(signal)
            
            logger.info(f"🎯 Generated {len(signals)} institutional signals")
            return signals
            
        except Exception as e:
            logger.error("❌ Error generating institutional signals", error=str(e), exc_info=True)
            return []
    
    async def _update_institutional_data(self) -> None:
        """Update institutional data from various sources."""
        try:
            # Check if update is needed (daily update)
            if (self.last_data_update and 
                datetime.now() - self.last_data_update < timedelta(hours=12)):
                return
            
            # In production, you'd integrate with:
            # - SEC EDGAR API for 13F filings
            # - Options flow data providers
            # - Insider trading databases
            # - Institutional holdings APIs
            
            # For demo, simulate institutional data
            await self._simulate_institutional_data()
            
            self.last_data_update = datetime.now()
            logger.info("✅ Institutional data updated")
            
        except Exception as e:
            logger.error("❌ Error updating institutional data", error=str(e))
    
    async def _simulate_institutional_data(self) -> None:
        """Simulate institutional data for demonstration."""
        current_date = datetime.now()
        
        # Simulate 13F filings
        simulated_13f = {
            'AAPL': [
                {
                    'institution': 'Berkshire Hathaway',
                    'shares': 915560000,
                    'value': 150000000000,
                    'change_pct': 0.02,  # 2% increase
                    'filing_date': current_date - timedelta(days=1),
                    'quarter': 'Q4 2024'
                }
            ],
            'NVDA': [
                {
                    'institution': 'Renaissance Technologies',
                    'shares': 5000000,
                    'value': 2500000000,
                    'change_pct': 0.15,  # 15% increase (new position)
                    'filing_date': current_date - timedelta(days=2),
                    'quarter': 'Q4 2024'
                }
            ]
        }
        
        # Simulate options flow
        simulated_options = {
            'TSLA': [
                {
                    'type': 'call_sweep',
                    'strike': 250,
                    'expiry': current_date + timedelta(days=30),
                    'volume': 10000,
                    'premium': 2500000,  # $2.5M
                    'timestamp': current_date - timedelta(hours=2),
                    'sentiment': 'bullish'
                }
            ]
        }
        
        # Simulate insider activity
        simulated_insider = {
            'META': [
                {
                    'insider_name': 'Mark Zuckerberg',
                    'transaction_type': 'purchase',
                    'shares': 100000,
                    'price': 350.00,
                    'value': 35000000,
                    'filing_date': current_date - timedelta(days=1),
                    'relationship': 'CEO'
                }
            ]
        }
        
        self.institutional_positions.update(simulated_13f)
        self.options_flow_data.update(simulated_options)
        self.insider_activity.update(simulated_insider)
    
    async def _analyze_13f_filings(self) -> List[Dict[str, Any]]:
        """Analyze 13F filings for new institutional positions."""
        signals = []
        
        try:
            for symbol, filings in self.institutional_positions.items():
                for filing in filings:
                    # Check if this is a significant new position or increase
                    change_pct = filing.get('change_pct', 0)
                    position_value = filing.get('value', 0)
                    filing_date = filing.get('filing_date')
                    
                    # Criteria for following
                    if (change_pct > 0.05 and  # 5% increase
                        position_value > self.min_position_size and
                        filing_date and
                        (datetime.now() - filing_date).days <= self.max_follow_age_days):
                        
                        # Check if enough time has passed (avoid front-running)
                        days_since_filing = (datetime.now() - filing_date).days
                        if days_since_filing >= self.follow_delay_days:
                            
                            signal = await self._create_institutional_signal(
                                symbol, filing, 'thirteenf_filing'
                            )
                            if signal:
                                signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.error("❌ Error analyzing 13F filings", error=str(e))
            return []
    
    async def _analyze_options_flow(self) -> List[Dict[str, Any]]:
        """Analyze unusual options activity."""
        signals = []
        
        try:
            for symbol, flows in self.options_flow_data.items():
                for flow in flows:
                    premium = flow.get('premium', 0)
                    sentiment = flow.get('sentiment', 'neutral')
                    timestamp = flow.get('timestamp')
                    
                    # Check for large options trades
                    if (premium > self.options_flow_threshold and
                        sentiment == 'bullish' and
                        timestamp and
                        (datetime.now() - timestamp).hours <= 24):
                        
                        signal = await self._create_institutional_signal(
                            symbol, flow, 'options_flow'
                        )
                        if signal:
                            signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.error("❌ Error analyzing options flow", error=str(e))
            return []
    
    async def _analyze_insider_activity(self) -> List[Dict[str, Any]]:
        """Analyze insider trading activity."""
        signals = []
        
        try:
            for symbol, activities in self.insider_activity.items():
                for activity in activities:
                    transaction_type = activity.get('transaction_type')
                    value = activity.get('value', 0)
                    filing_date = activity.get('filing_date')
                    relationship = activity.get('relationship', '')
                    
                    # Focus on purchases by key insiders
                    if (transaction_type == 'purchase' and
                        value > 1000000 and  # $1M+ purchase
                        relationship in ['CEO', 'CFO', 'President', 'Director'] and
                        filing_date and
                        (datetime.now() - filing_date).days <= 7):
                        
                        signal = await self._create_institutional_signal(
                            symbol, activity, 'insider_purchase'
                        )
                        if signal:
                            signals.append(signal)
            
            return signals
            
        except Exception as e:
            logger.error("❌ Error analyzing insider activity", error=str(e))
            return []
    
    async def _create_institutional_signal(
        self,
        symbol: str,
        data: Dict[str, Any],
        signal_type: str
    ) -> Optional[Dict[str, Any]]:
        """Create an institutional following signal."""
        try:
            # Get current market data
            market_data = await self.get_market_data([symbol])
            if not market_data or symbol not in market_data:
                return None
            
            current_price = market_data[symbol].get('last', 0)
            if not current_price:
                return None
            
            # Calculate confidence based on signal type and data quality
            confidence = await self._calculate_institutional_confidence(symbol, data, signal_type)
            
            if confidence < 0.6:
                return None
            
            # Calculate stop loss and take profit
            stop_loss_price = current_price * 0.92  # 8% stop loss for institutional plays
            take_profit_price = current_price * 1.20  # 20% take profit
            
            signal = {
                'symbol': symbol,
                'side': 'BUY',
                'order_type': 'LIMIT',
                'entry_price': current_price,
                'stop_loss': stop_loss_price,
                'take_profit': take_profit_price,
                'confidence': confidence,
                'reason': f'Institutional activity - {signal_type}',
                'strategy_data': {
                    'signal_type': signal_type,
                    'institutional_data': data,
                    'follow_delay_days': self.follow_delay_days,
                    'entry_reason': f'{signal_type}_follow'
                }
            }
            
            logger.info(
                f"🎯 Institutional signal generated",
                symbol=symbol,
                signal_type=signal_type,
                confidence=confidence,
                institution=data.get('institution', 'Unknown')
            )
            
            return signal
            
        except Exception as e:
            logger.error(f"❌ Error creating institutional signal for {symbol}", error=str(e))
            return None
    
    async def _calculate_institutional_confidence(
        self,
        symbol: str,
        data: Dict[str, Any],
        signal_type: str
    ) -> float:
        """Calculate confidence score for institutional signal."""
        try:
            base_confidence = {
                'thirteenf_filing': 0.7,
                'options_flow': 0.6,
                'insider_purchase': 0.8
            }.get(signal_type, 0.5)
            
            # Adjust based on data quality
            if signal_type == 'thirteenf_filing':
                change_pct = data.get('change_pct', 0)
                position_value = data.get('value', 0)
                
                # Higher confidence for larger changes and positions
                size_multiplier = min(2.0, position_value / self.min_position_size)
                change_multiplier = min(2.0, change_pct / 0.1)  # Max at 10% change
                
                confidence = base_confidence * (size_multiplier + change_multiplier) / 2
                
            elif signal_type == 'options_flow':
                premium = data.get('premium', 0)
                volume = data.get('volume', 0)
                
                # Higher confidence for larger premiums and volumes
                premium_multiplier = min(2.0, premium / self.options_flow_threshold)
                volume_multiplier = min(2.0, volume / 5000)  # Max at 5000 contracts
                
                confidence = base_confidence * (premium_multiplier + volume_multiplier) / 2
                
            elif signal_type == 'insider_purchase':
                value = data.get('value', 0)
                relationship = data.get('relationship', '')
                
                # Higher confidence for CEO/CFO purchases
                role_multiplier = 2.0 if relationship in ['CEO', 'CFO'] else 1.5
                value_multiplier = min(2.0, value / 5000000)  # Max at $5M
                
                confidence = base_confidence * (role_multiplier + value_multiplier) / 2
            
            else:
                confidence = base_confidence
            
            # Cap confidence at 0.95
            return min(0.95, confidence)
            
        except Exception as e:
            logger.error(f"❌ Error calculating institutional confidence", error=str(e))
            return 0.5
    
    async def _deduplicate_and_rank_signals(self, signals: List[Dict]) -> List[Dict]:
        """Remove duplicate signals and rank by confidence."""
        try:
            # Remove duplicates by symbol
            unique_signals = {}
            for signal in signals:
                symbol = signal['symbol']
                if symbol not in unique_signals or signal['confidence'] > unique_signals[symbol]['confidence']:
                    unique_signals[symbol] = signal
            
            # Sort by confidence (highest first)
            ranked_signals = sorted(
                unique_signals.values(),
                key=lambda x: x['confidence'],
                reverse=True
            )
            
            # Limit to top 5 signals to avoid over-diversification
            return ranked_signals[:5]
            
        except Exception as e:
            logger.error("❌ Error deduplicating signals", error=str(e))
            return signals
    
    async def should_exit_position(self, symbol: str, position_data: Dict) -> Tuple[bool, str]:
        """Determine if an institutional position should be exited."""
        try:
            # Get current market data
            market_data = await self.get_market_data([symbol])
            if not market_data or symbol not in market_data:
                return False, "No market data available"
            
            current_price = market_data[symbol].get('last', 0)
            entry_price = position_data.get('avg_cost', 0)
            
            if not current_price or not entry_price:
                return False, "Invalid price data"
            
            # Check stop loss
            stop_loss = position_data.get('stop_loss')
            if stop_loss and current_price <= stop_loss:
                return True, f"Stop loss triggered at {current_price}"
            
            # Check take profit
            take_profit = position_data.get('take_profit')
            if take_profit and current_price >= take_profit:
                return True, f"Take profit reached at {current_price}"
            
            # Check for institutional exit signals
            exit_signal = await self._check_institutional_exit(symbol)
            if exit_signal['should_exit']:
                return True, exit_signal['reason']
            
            # Check maximum hold period (institutional plays can be longer-term)
            entry_date = position_data.get('entry_date')
            if entry_date:
                days_held = (datetime.now() - entry_date).days
                if days_held >= 60:  # 60-day maximum for institutional follows
                    return True, f"Maximum hold period reached ({days_held} days)"
            
            return False, "Hold position"
            
        except Exception as e:
            logger.error(f"❌ Error checking exit condition for {symbol}", error=str(e))
            return False, f"Error checking exit: {str(e)}"
    
    async def _check_institutional_exit(self, symbol: str) -> Dict[str, Any]:
        """Check for institutional exit signals."""
        try:
            # Check for recent institutional selling
            recent_filings = self.institutional_positions.get(symbol, [])
            
            for filing in recent_filings:
                filing_date = filing.get('filing_date')
                change_pct = filing.get('change_pct', 0)
                
                # Check for recent large sales
                if (filing_date and
                    (datetime.now() - filing_date).days <= 7 and
                    change_pct < -0.1):  # 10% reduction
                    
                    return {
                        'should_exit': True,
                        'reason': f'Institutional selling detected: {change_pct:.1%} reduction'
                    }
            
            # Check for negative options flow
            recent_options = self.options_flow_data.get(symbol, [])
            
            for flow in recent_options:
                timestamp = flow.get('timestamp')
                sentiment = flow.get('sentiment')
                premium = flow.get('premium', 0)
                
                if (timestamp and
                    (datetime.now() - timestamp).hours <= 24 and
                    sentiment == 'bearish' and
                    premium > self.options_flow_threshold):
                    
                    return {
                        'should_exit': True,
                        'reason': f'Large bearish options flow detected: ${premium:,.0f}'
                    }
            
            return {'should_exit': False, 'reason': 'No exit signals detected'}
            
        except Exception as e:
            logger.error(f"❌ Error checking institutional exit for {symbol}", error=str(e))
            return {'should_exit': False, 'reason': f'Error: {str(e)}'}
    
    async def _strategy_specific_validation(self, signal: Dict[str, Any]) -> Tuple[bool, str]:
        """Institutional strategy specific validation."""
        try:
            # Check if we already have a position in this symbol
            if signal['symbol'] in self.active_positions:
                return False, "Already have position in this symbol"
            
            # Validate confidence threshold (higher for institutional)
            if signal.get('confidence', 0) < 0.65:
                return False, f"Confidence too low: {signal.get('confidence', 0):.2f}"
            
            # Check signal freshness
            strategy_data = signal.get('strategy_data', {})
            institutional_data = strategy_data.get('institutional_data', {})
            
            # Validate data recency based on signal type
            signal_type = strategy_data.get('signal_type')
            
            if signal_type == 'thirteenf_filing':
                filing_date = institutional_data.get('filing_date')
                if filing_date and (datetime.now() - filing_date).days > self.max_follow_age_days:
                    return False, f"Filing too old: {(datetime.now() - filing_date).days} days"
            
            elif signal_type == 'options_flow':
                timestamp = institutional_data.get('timestamp')
                if timestamp and (datetime.now() - timestamp).hours > 48:
                    return False, "Options flow too old"
            
            elif signal_type == 'insider_purchase':
                filing_date = institutional_data.get('filing_date')
                if filing_date and (datetime.now() - filing_date).days > 14:
                    return False, "Insider activity too old"
            
            return True, "Institutional validation passed"
            
        except Exception as e:
            logger.error("❌ Error in institutional validation", error=str(e))
            return False, f"Validation error: {str(e)}"
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """Get strategy information and current state."""
        return {
            'name': self.name,
            'type': 'institutional_following',
            'enabled': self.is_enabled,
            'parameters': {
                'min_position_size': self.min_position_size,
                'follow_delay_days': self.follow_delay_days,
                'min_ownership_pct': self.min_ownership_pct,
                'max_follow_age_days': self.max_follow_age_days
            },
            'tracked_institutions': len(self.tracked_institutions),
            'active_positions': len(self.active_positions),
            'institutional_positions_tracked': len(self.institutional_positions),
            'options_flow_tracked': len(self.options_flow_data),
            'insider_activity_tracked': len(self.insider_activity),
            'performance': self.get_performance_metrics(),
            'last_data_update': self.last_data_update
        }
