"""
Trades API Endpoint
Trading history, execution tracking, and trade management.

Author: Hector<PERSON>a1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import asyncio
import json
import os
import sys
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional
import structlog
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.database import DatabaseManager
from schwab_client import SchwabClient

logger = structlog.get_logger(__name__)


async def get_trades_data(
    symbol: str = None,
    strategy: str = None,
    start_date: str = None,
    end_date: str = None,
    limit: int = 100,
    include_analytics: bool = True
) -> Dict[str, Any]:
    """
    Get trading history and analytics.
    
    Args:
        symbol: Filter by symbol
        strategy: Filter by strategy
        start_date: Start date (ISO format)
        end_date: End date (ISO format)
        limit: Maximum number of trades
        include_analytics: Include trade analytics
        
    Returns:
        Dict with trades data and analytics
    """
    try:
        trades_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'trades': [],
            'summary': {},
            'analytics': {},
            'filters': {
                'symbol': symbol,
                'strategy': strategy,
                'start_date': start_date,
                'end_date': end_date,
                'limit': limit
            }
        }
        
        # Initialize database
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        # Parse date filters
        start_dt = datetime.fromisoformat(start_date) if start_date else None
        end_dt = datetime.fromisoformat(end_date) if end_date else None
        
        # Get trades from database
        trades = await db_manager.get_trades(
            symbol=symbol,
            strategy=strategy,
            start_date=start_dt,
            end_date=end_dt,
            limit=limit
        )
        
        # Enhance trade data
        enhanced_trades = []
        for trade in trades:
            enhanced_trade = await _enhance_trade_data(trade)
            enhanced_trades.append(enhanced_trade)
        
        trades_data['trades'] = enhanced_trades
        
        # Calculate summary statistics
        trades_data['summary'] = await _calculate_trades_summary(enhanced_trades)
        
        # Calculate analytics if requested
        if include_analytics:
            trades_data['analytics'] = await _calculate_trades_analytics(enhanced_trades)
        
        await db_manager.close()
        
        logger.info(
            "📊 Trades data retrieved",
            count=len(enhanced_trades),
            symbol=symbol,
            strategy=strategy
        )
        
        return trades_data
        
    except Exception as e:
        logger.error("❌ Error getting trades data", error=str(e), exc_info=True)
        return {
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }


async def execute_manual_trade(trade_request: Dict[str, Any]) -> Dict[str, Any]:
    """
    Execute a manual trade (admin only).
    
    Args:
        trade_request: Trade parameters
        
    Returns:
        Trade execution result
    """
    try:
        # Validate trade request
        validation_result = await _validate_trade_request(trade_request)
        if not validation_result['valid']:
            return {
                'success': False,
                'error': validation_result['error'],
                'timestamp': datetime.utcnow().isoformat()
            }
        
        # Initialize Schwab client
        schwab_client = await _get_schwab_client()
        
        # Execute trade
        order_result = await schwab_client.place_order(
            symbol=trade_request['symbol'],
            quantity=trade_request['quantity'],
            order_type=trade_request['order_type'],
            side=trade_request['side'],
            price=trade_request.get('price')
        )
        
        # Log trade to database
        if order_result.get('success'):
            db_manager = DatabaseManager()
            await db_manager.initialize()
            
            trade_data = {
                'symbol': trade_request['symbol'],
                'side': trade_request['side'],
                'quantity': trade_request['quantity'],
                'price': trade_request.get('price', 0),
                'order_type': trade_request['order_type'],
                'strategy': trade_request.get('strategy', 'manual'),
                'order_id': order_result.get('order_id'),
                'status': 'PENDING'
            }
            
            trade_id = await db_manager.log_trade(trade_data)
            await db_manager.close()
            
            order_result['trade_id'] = trade_id
        
        await schwab_client.close()
        
        logger.info(
            "📊 Manual trade executed",
            symbol=trade_request['symbol'],
            side=trade_request['side'],
            quantity=trade_request['quantity'],
            success=order_result.get('success')
        )
        
        return order_result
        
    except Exception as e:
        logger.error("❌ Error executing manual trade", error=str(e), exc_info=True)
        return {
            'success': False,
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }


async def _enhance_trade_data(trade: Dict[str, Any]) -> Dict[str, Any]:
    """Enhance trade data with additional information."""
    try:
        enhanced_trade = dict(trade)
        
        # Calculate trade metrics
        if 'price' in trade and 'quantity' in trade:
            enhanced_trade['total_value'] = trade['price'] * trade['quantity']
        
        # Add time-based metrics
        if 'timestamp' in trade:
            trade_time = trade['timestamp']
            if isinstance(trade_time, str):
                trade_time = datetime.fromisoformat(trade_time)
            
            enhanced_trade['days_ago'] = (datetime.utcnow() - trade_time).days
            enhanced_trade['formatted_time'] = trade_time.strftime('%Y-%m-%d %H:%M:%S')
        
        # Add performance metrics (if exit data available)
        if 'exit_price' in trade and 'price' in trade:
            entry_price = trade['price']
            exit_price = trade['exit_price']
            side = trade.get('side', 'BUY')
            
            if side == 'BUY':
                pnl_pct = ((exit_price - entry_price) / entry_price) * 100
            else:  # SELL (short)
                pnl_pct = ((entry_price - exit_price) / entry_price) * 100
            
            enhanced_trade['pnl_percent'] = round(pnl_pct, 2)
            enhanced_trade['pnl_amount'] = round(pnl_pct / 100 * enhanced_trade.get('total_value', 0), 2)
        
        return enhanced_trade
        
    except Exception as e:
        logger.error("❌ Error enhancing trade data", error=str(e))
        return trade


async def _calculate_trades_summary(trades: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Calculate summary statistics for trades."""
    try:
        if not trades:
            return {
                'total_trades': 0,
                'total_volume': 0,
                'avg_trade_size': 0,
                'win_rate': 0,
                'total_pnl': 0
            }
        
        total_trades = len(trades)
        total_volume = sum(trade.get('total_value', 0) for trade in trades)
        avg_trade_size = total_volume / total_trades if total_trades > 0 else 0
        
        # Calculate P&L metrics
        completed_trades = [t for t in trades if 'pnl_amount' in t]
        
        if completed_trades:
            winning_trades = sum(1 for t in completed_trades if t.get('pnl_amount', 0) > 0)
            win_rate = (winning_trades / len(completed_trades)) * 100
            total_pnl = sum(t.get('pnl_amount', 0) for t in completed_trades)
            avg_win = np.mean([t['pnl_amount'] for t in completed_trades if t['pnl_amount'] > 0]) if winning_trades > 0 else 0
            avg_loss = np.mean([t['pnl_amount'] for t in completed_trades if t['pnl_amount'] < 0]) if len(completed_trades) - winning_trades > 0 else 0
        else:
            win_rate = 0
            total_pnl = 0
            avg_win = 0
            avg_loss = 0
        
        # Strategy breakdown
        strategy_breakdown = {}
        for trade in trades:
            strategy = trade.get('strategy', 'Unknown')
            if strategy not in strategy_breakdown:
                strategy_breakdown[strategy] = {'count': 0, 'volume': 0}
            
            strategy_breakdown[strategy]['count'] += 1
            strategy_breakdown[strategy]['volume'] += trade.get('total_value', 0)
        
        return {
            'total_trades': total_trades,
            'completed_trades': len(completed_trades),
            'pending_trades': total_trades - len(completed_trades),
            'total_volume': round(total_volume, 2),
            'avg_trade_size': round(avg_trade_size, 2),
            'win_rate': round(win_rate, 1),
            'total_pnl': round(total_pnl, 2),
            'avg_win': round(avg_win, 2),
            'avg_loss': round(avg_loss, 2),
            'profit_factor': round(abs(avg_win / avg_loss), 2) if avg_loss != 0 else 0,
            'strategy_breakdown': strategy_breakdown
        }
        
    except Exception as e:
        logger.error("❌ Error calculating trades summary", error=str(e))
        return {'error': str(e)}


async def _calculate_trades_analytics(trades: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Calculate advanced trade analytics."""
    try:
        if not trades:
            return {}
        
        # Time-based analysis
        time_analysis = await _analyze_trade_timing(trades)
        
        # Symbol analysis
        symbol_analysis = await _analyze_symbol_performance(trades)
        
        # Strategy analysis
        strategy_analysis = await _analyze_strategy_performance(trades)
        
        # Risk analysis
        risk_analysis = await _analyze_trade_risks(trades)
        
        return {
            'time_analysis': time_analysis,
            'symbol_analysis': symbol_analysis,
            'strategy_analysis': strategy_analysis,
            'risk_analysis': risk_analysis,
            'last_updated': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("❌ Error calculating trades analytics", error=str(e))
        return {'error': str(e)}


async def _validate_trade_request(trade_request: Dict[str, Any]) -> Dict[str, Any]:
    """Validate manual trade request."""
    try:
        required_fields = ['symbol', 'side', 'quantity', 'order_type']
        
        # Check required fields
        for field in required_fields:
            if field not in trade_request:
                return {
                    'valid': False,
                    'error': f'Missing required field: {field}'
                }
        
        # Validate symbol
        symbol = trade_request['symbol']
        if not isinstance(symbol, str) or len(symbol) < 1 or len(symbol) > 5:
            return {
                'valid': False,
                'error': f'Invalid symbol: {symbol}'
            }
        
        # Validate side
        if trade_request['side'] not in ['BUY', 'SELL']:
            return {
                'valid': False,
                'error': f'Invalid side: {trade_request["side"]}'
            }
        
        # Validate quantity
        quantity = trade_request['quantity']
        if not isinstance(quantity, int) or quantity <= 0:
            return {
                'valid': False,
                'error': f'Invalid quantity: {quantity}'
            }
        
        # Validate order type
        if trade_request['order_type'] not in ['MARKET', 'LIMIT', 'STOP', 'STOP_LIMIT']:
            return {
                'valid': False,
                'error': f'Invalid order type: {trade_request["order_type"]}'
            }
        
        # Validate price for limit orders
        if trade_request['order_type'] in ['LIMIT', 'STOP_LIMIT']:
            price = trade_request.get('price')
            if not price or not isinstance(price, (int, float)) or price <= 0:
                return {
                    'valid': False,
                    'error': 'Price required for limit orders'
                }
        
        return {'valid': True, 'error': None}
        
    except Exception as e:
        logger.error("❌ Error validating trade request", error=str(e))
        return {
            'valid': False,
            'error': f'Validation error: {str(e)}'
        }


async def _get_schwab_client() -> SchwabClient:
    """Initialize Schwab client for API calls."""
    client = SchwabClient(
        client_id=os.getenv('SCHWAB_CLIENT_ID'),
        client_secret=os.getenv('SCHWAB_CLIENT_SECRET'),
        redirect_uri=os.getenv('SCHWAB_REDIRECT_URI'),
        account_number=os.getenv('SCHWAB_ACCOUNT_NUMBER')
    )
    await client.initialize()
    return client


# Serverless handlers
async def get_trades_handler(event, context):
    """Serverless function handler for GET /trades."""
    try:
        # Parse query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        
        trades_data = await get_trades_data(
            symbol=query_params.get('symbol'),
            strategy=query_params.get('strategy'),
            start_date=query_params.get('start_date'),
            end_date=query_params.get('end_date'),
            limit=int(query_params.get('limit', 100)),
            include_analytics=query_params.get('analytics', 'true').lower() == 'true'
        )
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps(trades_data, default=str)
        }
        
    except Exception as e:
        logger.error("❌ Get trades endpoint error", error=str(e))
        return {
            'statusCode': 500,
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            })
        }


async def post_trades_handler(event, context):
    """Serverless function handler for POST /trades."""
    try:
        # Parse request body
        body = json.loads(event.get('body', '{}'))
        
        # Execute manual trade
        result = await execute_manual_trade(body)
        
        status_code = 200 if result.get('success') else 400
        
        return {
            'statusCode': status_code,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps(result, default=str)
        }
        
    except Exception as e:
        logger.error("❌ Post trades endpoint error", error=str(e))
        return {
            'statusCode': 500,
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            })
        }
