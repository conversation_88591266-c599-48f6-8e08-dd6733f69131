[tool:pytest]
# Pytest configuration for Schwab Telegram Trading Bot

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Minimum version
minversion = 7.0

# Add options
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=src
    --cov-report=html:htmlcov
    --cov-report=term-missing
    --cov-report=xml
    --cov-fail-under=80
    --asyncio-mode=auto
    --durations=10

# Markers
markers =
    unit: Unit tests
    integration: Integration tests requiring external services
    slow: Slow tests that take more than 5 seconds
    api: API endpoint tests
    strategy: Trading strategy tests
    risk: Risk management tests
    database: Database tests
    auth: Authentication tests

# Asyncio configuration
asyncio_mode = auto

# Warnings
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:aiohttp.*

# Test timeout
timeout = 300

# Parallel execution
# -n auto for pytest-xdist if installed
