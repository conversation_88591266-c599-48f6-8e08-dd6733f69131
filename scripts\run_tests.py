#!/usr/bin/env python3
"""
Test Runner Script
Comprehensive test execution with coverage reporting and CI/CD integration.

Author: Hector<PERSON>a1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path
from datetime import datetime
import json


class TestRunner:
    """Comprehensive test runner with multiple execution modes."""
    
    def __init__(self):
        """Initialize test runner."""
        self.project_root = Path(__file__).parent.parent
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'skipped_tests': 0,
            'coverage_percent': 0,
            'duration_seconds': 0,
            'test_files': []
        }
    
    def run_tests(
        self,
        test_type: str = 'all',
        coverage: bool = True,
        verbose: bool = True,
        parallel: bool = False,
        output_format: str = 'terminal'
    ) -> bool:
        """
        Run tests with specified configuration.
        
        Args:
            test_type: Type of tests to run ('unit', 'integration', 'all')
            coverage: Generate coverage report
            verbose: Verbose output
            parallel: Run tests in parallel
            output_format: Output format ('terminal', 'json', 'xml')
            
        Returns:
            True if all tests passed, False otherwise
        """
        print("🧪 Starting test execution...")
        print("=" * 60)
        
        try:
            # Prepare test command
            cmd = self._build_test_command(
                test_type, coverage, verbose, parallel, output_format
            )
            
            # Run tests
            start_time = datetime.now()
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            end_time = datetime.now()
            
            # Process results
            self.test_results['duration_seconds'] = (end_time - start_time).total_seconds()
            success = self._process_test_results(result, output_format)
            
            # Generate reports
            if coverage:
                self._generate_coverage_report()
            
            self._generate_test_report(output_format)
            
            # Display summary
            self._display_summary(success)
            
            return success
            
        except Exception as e:
            print(f"❌ Test execution failed: {e}")
            return False
    
    def _build_test_command(
        self,
        test_type: str,
        coverage: bool,
        verbose: bool,
        parallel: bool,
        output_format: str
    ) -> List[str]:
        """Build pytest command with options."""
        cmd = [sys.executable, '-m', 'pytest']
        
        # Test selection
        if test_type == 'unit':
            cmd.extend(['-m', 'unit'])
        elif test_type == 'integration':
            cmd.extend(['-m', 'integration'])
        elif test_type == 'api':
            cmd.extend(['-m', 'api'])
        elif test_type == 'strategy':
            cmd.extend(['-m', 'strategy'])
        # 'all' runs everything (no marker filter)
        
        # Coverage options
        if coverage:
            cmd.extend([
                '--cov=src',
                '--cov-report=html:htmlcov',
                '--cov-report=term-missing',
                '--cov-report=xml:coverage.xml',
                '--cov-fail-under=70'
            ])
        
        # Verbosity
        if verbose:
            cmd.append('-v')
        else:
            cmd.append('-q')
        
        # Parallel execution
        if parallel:
            try:
                import pytest_xdist
                cmd.extend(['-n', 'auto'])
            except ImportError:
                print("⚠️ pytest-xdist not installed, running sequentially")
        
        # Output format
        if output_format == 'json':
            cmd.extend(['--json-report', '--json-report-file=test_results.json'])
        elif output_format == 'xml':
            cmd.extend(['--junitxml=test_results.xml'])
        
        # Additional options
        cmd.extend([
            '--tb=short',
            '--durations=10',
            '--strict-markers',
            '--asyncio-mode=auto'
        ])
        
        return cmd
    
    def _process_test_results(self, result: subprocess.CompletedProcess, output_format: str) -> bool:
        """Process test execution results."""
        try:
            # Parse pytest output
            output_lines = result.stdout.split('\n')
            
            # Look for test summary line
            for line in output_lines:
                if 'passed' in line and ('failed' in line or 'error' in line or 'skipped' in line):
                    # Parse summary line like "5 passed, 2 failed, 1 skipped"
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if part == 'passed' and i > 0:
                            self.test_results['passed_tests'] = int(parts[i-1])
                        elif part == 'failed' and i > 0:
                            self.test_results['failed_tests'] = int(parts[i-1])
                        elif part == 'skipped' and i > 0:
                            self.test_results['skipped_tests'] = int(parts[i-1])
                    break
            
            # Calculate total tests
            self.test_results['total_tests'] = (
                self.test_results['passed_tests'] +
                self.test_results['failed_tests'] +
                self.test_results['skipped_tests']
            )
            
            # Check if tests passed
            success = result.returncode == 0
            
            # Print output
            if result.stdout:
                print("📋 Test Output:")
                print(result.stdout)
            
            if result.stderr:
                print("⚠️ Test Errors:")
                print(result.stderr)
            
            return success
            
        except Exception as e:
            print(f"❌ Error processing test results: {e}")
            return False
    
    def _generate_coverage_report(self) -> None:
        """Generate and parse coverage report."""
        try:
            # Try to read coverage data
            coverage_file = self.project_root / '.coverage'
            if coverage_file.exists():
                # Run coverage report to get percentage
                result = subprocess.run([
                    sys.executable, '-m', 'coverage', 'report', '--show-missing'
                ], cwd=self.project_root, capture_output=True, text=True)
                
                # Parse coverage percentage
                for line in result.stdout.split('\n'):
                    if 'TOTAL' in line:
                        parts = line.split()
                        if len(parts) >= 4 and '%' in parts[-1]:
                            coverage_pct = parts[-1].replace('%', '')
                            self.test_results['coverage_percent'] = float(coverage_pct)
                        break
            
            print(f"📊 Coverage report generated: {self.test_results['coverage_percent']:.1f}%")
            
        except Exception as e:
            print(f"⚠️ Error generating coverage report: {e}")
    
    def _generate_test_report(self, output_format: str) -> None:
        """Generate test execution report."""
        try:
            # Save test results
            results_file = self.project_root / f'test_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            
            with open(results_file, 'w') as f:
                json.dump(self.test_results, f, indent=2)
            
            print(f"📄 Test report saved: {results_file}")
            
        except Exception as e:
            print(f"⚠️ Error generating test report: {e}")
    
    def _display_summary(self, success: bool) -> None:
        """Display test execution summary."""
        print("\n" + "=" * 60)
        print("🧪 TEST EXECUTION SUMMARY")
        print("=" * 60)
        
        status_emoji = "✅" if success else "❌"
        status_text = "PASSED" if success else "FAILED"
        
        print(f"{status_emoji} Overall Status: {status_text}")
        print(f"📊 Total Tests: {self.test_results['total_tests']}")
        print(f"✅ Passed: {self.test_results['passed_tests']}")
        print(f"❌ Failed: {self.test_results['failed_tests']}")
        print(f"⏭️ Skipped: {self.test_results['skipped_tests']}")
        print(f"📈 Coverage: {self.test_results['coverage_percent']:.1f}%")
        print(f"⏱️ Duration: {self.test_results['duration_seconds']:.1f}s")
        
        if not success:
            print("\n⚠️ Some tests failed. Check the output above for details.")
            print("💡 Tips:")
            print("   - Run with -v for more verbose output")
            print("   - Use --tb=long for detailed tracebacks")
            print("   - Check logs in logs/ directory")
        
        print("\n📁 Reports Generated:")
        print("   - HTML Coverage: htmlcov/index.html")
        print("   - XML Coverage: coverage.xml")
        print("   - Test Results: test_results_*.json")


def main():
    """Main test runner function."""
    parser = argparse.ArgumentParser(
        description="Run tests for Schwab Telegram Trading Bot"
    )
    
    parser.add_argument(
        '--type',
        choices=['unit', 'integration', 'api', 'strategy', 'all'],
        default='all',
        help='Type of tests to run'
    )
    
    parser.add_argument(
        '--no-coverage',
        action='store_true',
        help='Skip coverage reporting'
    )
    
    parser.add_argument(
        '--quiet',
        action='store_true',
        help='Quiet output'
    )
    
    parser.add_argument(
        '--parallel',
        action='store_true',
        help='Run tests in parallel'
    )
    
    parser.add_argument(
        '--format',
        choices=['terminal', 'json', 'xml'],
        default='terminal',
        help='Output format'
    )
    
    args = parser.parse_args()
    
    # Create test runner
    runner = TestRunner()
    
    # Run tests
    success = runner.run_tests(
        test_type=args.type,
        coverage=not args.no_coverage,
        verbose=not args.quiet,
        parallel=args.parallel,
        output_format=args.format
    )
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
