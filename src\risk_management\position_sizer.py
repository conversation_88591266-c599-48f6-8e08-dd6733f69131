"""
Position Sizer
Advanced position sizing algorithms with multiple methodologies and risk controls.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import structlog
import numpy as np
from math import sqrt, log

logger = structlog.get_logger(__name__)


class PositionSizer:
    """
    Advanced position sizing with multiple algorithms:
    - Fixed percentage risk
    - Kelly Criterion
    - Volatility-adjusted sizing
    - Market cap weighted
    - Equal dollar weighting
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize position sizer."""
        self.config = config
        
        # Position sizing parameters
        self.default_risk_pct = config.get('max_portfolio_risk', 0.02)  # 2%
        self.max_position_pct = config.get('max_position_size', 0.05)   # 5%
        self.min_position_value = config.get('min_position_value', 100) # $100
        self.max_position_value = config.get('max_position_value', 50000) # $50K
        
        # Sizing method
        self.sizing_method = config.get('position_sizing_method', 'fixed_percentage')
        
        # Kelly Criterion parameters
        self.kelly_lookback_trades = config.get('kelly_lookback_trades', 50)
        self.kelly_max_fraction = config.get('kelly_max_fraction', 0.25)  # Max 25% Kelly
        
        # Volatility adjustment parameters
        self.volatility_lookback_days = config.get('volatility_lookback_days', 20)
        self.base_volatility = config.get('base_volatility', 0.02)  # 2% daily volatility
        
        # Performance tracking
        self.sizing_history: List[Dict] = []
        
        logger.info(
            "✅ Position sizer initialized",
            method=self.sizing_method,
            default_risk=self.default_risk_pct,
            max_position=self.max_position_pct
        )
    
    async def calculate_size(
        self,
        account_value: float,
        entry_price: float,
        stop_loss: Optional[float] = None,
        risk_per_trade: Optional[float] = None,
        symbol: str = None,
        strategy: str = None,
        confidence: float = 1.0
    ) -> int:
        """
        Calculate optimal position size using configured method.
        
        Args:
            account_value: Total account value
            entry_price: Entry price for the position
            stop_loss: Stop loss price (if available)
            risk_per_trade: Risk percentage for this trade
            symbol: Stock symbol for volatility analysis
            strategy: Strategy name for historical analysis
            confidence: Signal confidence (0.0 to 1.0)
            
        Returns:
            Number of shares to trade
        """
        try:
            # Use provided risk or default
            risk_pct = risk_per_trade or self.default_risk_pct
            
            # Calculate position size based on method
            if self.sizing_method == 'fixed_percentage':
                shares = await self._fixed_percentage_sizing(
                    account_value, entry_price, stop_loss, risk_pct
                )
            
            elif self.sizing_method == 'kelly_criterion':
                shares = await self._kelly_criterion_sizing(
                    account_value, entry_price, stop_loss, symbol, strategy
                )
            
            elif self.sizing_method == 'volatility_adjusted':
                shares = await self._volatility_adjusted_sizing(
                    account_value, entry_price, symbol, risk_pct
                )
            
            elif self.sizing_method == 'equal_dollar':
                shares = await self._equal_dollar_sizing(
                    account_value, entry_price
                )
            
            else:
                logger.warning(f"⚠️ Unknown sizing method: {self.sizing_method}")
                shares = await self._fixed_percentage_sizing(
                    account_value, entry_price, stop_loss, risk_pct
                )
            
            # Apply confidence adjustment
            shares = int(shares * confidence)
            
            # Apply position limits
            shares = await self._apply_position_limits(
                shares, entry_price, account_value
            )
            
            # Record sizing decision
            await self._record_sizing_decision(
                symbol, shares, entry_price, account_value, risk_pct, confidence
            )
            
            logger.info(
                f"📏 Position size calculated",
                symbol=symbol,
                shares=shares,
                value=shares * entry_price,
                method=self.sizing_method,
                risk_pct=risk_pct,
                confidence=confidence
            )
            
            return shares
            
        except Exception as e:
            logger.error("❌ Error calculating position size", error=str(e))
            return 0
    
    async def _fixed_percentage_sizing(
        self,
        account_value: float,
        entry_price: float,
        stop_loss: Optional[float],
        risk_pct: float
    ) -> int:
        """Fixed percentage risk position sizing."""
        try:
            # Calculate risk amount
            risk_amount = account_value * risk_pct
            
            if stop_loss:
                # Risk per share based on stop loss
                risk_per_share = abs(entry_price - stop_loss)
                if risk_per_share > 0:
                    shares = int(risk_amount / risk_per_share)
                else:
                    shares = 0
            else:
                # Use default 2% risk per share if no stop loss
                risk_per_share = entry_price * 0.02
                shares = int(risk_amount / risk_per_share)
            
            return max(0, shares)
            
        except Exception as e:
            logger.error("❌ Error in fixed percentage sizing", error=str(e))
            return 0
    
    async def _kelly_criterion_sizing(
        self,
        account_value: float,
        entry_price: float,
        stop_loss: Optional[float],
        symbol: str,
        strategy: str
    ) -> int:
        """Kelly Criterion position sizing based on historical performance."""
        try:
            # Get historical performance for this strategy/symbol
            win_rate, avg_win, avg_loss = await self._get_historical_performance(
                symbol, strategy
            )
            
            if win_rate == 0 or avg_win == 0 or avg_loss == 0:
                # Fall back to fixed percentage if no history
                return await self._fixed_percentage_sizing(
                    account_value, entry_price, stop_loss, self.default_risk_pct
                )
            
            # Calculate Kelly fraction
            # Kelly = (bp - q) / b
            # where b = odds received (avg_win/avg_loss), p = win_rate, q = 1-p
            b = avg_win / avg_loss
            p = win_rate
            q = 1 - p
            
            kelly_fraction = (b * p - q) / b
            
            # Apply Kelly fraction limits
            kelly_fraction = max(0, min(kelly_fraction, self.kelly_max_fraction))
            
            # Calculate position size
            position_value = account_value * kelly_fraction
            shares = int(position_value / entry_price)
            
            logger.debug(
                f"📊 Kelly sizing calculated",
                symbol=symbol,
                win_rate=win_rate,
                avg_win=avg_win,
                avg_loss=avg_loss,
                kelly_fraction=kelly_fraction,
                shares=shares
            )
            
            return max(0, shares)
            
        except Exception as e:
            logger.error("❌ Error in Kelly criterion sizing", error=str(e))
            return 0
    
    async def _volatility_adjusted_sizing(
        self,
        account_value: float,
        entry_price: float,
        symbol: str,
        risk_pct: float
    ) -> int:
        """Volatility-adjusted position sizing."""
        try:
            # Get volatility estimate
            volatility = await self._estimate_volatility(symbol)
            
            if volatility <= 0:
                volatility = self.base_volatility
            
            # Adjust position size inversely to volatility
            # Higher volatility = smaller position
            volatility_adjustment = self.base_volatility / volatility
            volatility_adjustment = max(0.5, min(2.0, volatility_adjustment))  # Limit adjustment
            
            # Calculate base position size
            risk_amount = account_value * risk_pct
            risk_per_share = entry_price * volatility * 2  # 2 standard deviations
            
            shares = int((risk_amount / risk_per_share) * volatility_adjustment)
            
            logger.debug(
                f"📊 Volatility sizing calculated",
                symbol=symbol,
                volatility=volatility,
                adjustment=volatility_adjustment,
                shares=shares
            )
            
            return max(0, shares)
            
        except Exception as e:
            logger.error("❌ Error in volatility-adjusted sizing", error=str(e))
            return 0
    
    async def _equal_dollar_sizing(
        self,
        account_value: float,
        entry_price: float
    ) -> int:
        """Equal dollar weighting position sizing."""
        try:
            # Target position value (5% of account for equal weighting with 20 positions)
            target_positions = 20
            target_position_value = account_value / target_positions
            
            # Apply maximum position limit
            max_position_value = account_value * self.max_position_pct
            target_position_value = min(target_position_value, max_position_value)
            
            shares = int(target_position_value / entry_price)
            
            return max(0, shares)
            
        except Exception as e:
            logger.error("❌ Error in equal dollar sizing", error=str(e))
            return 0
    
    async def _apply_position_limits(
        self,
        shares: int,
        entry_price: float,
        account_value: float
    ) -> int:
        """Apply position size limits and constraints."""
        try:
            position_value = shares * entry_price
            
            # Check minimum position value
            if position_value < self.min_position_value:
                shares = max(1, int(self.min_position_value / entry_price))
                position_value = shares * entry_price
            
            # Check maximum position value
            if position_value > self.max_position_value:
                shares = int(self.max_position_value / entry_price)
                position_value = shares * entry_price
            
            # Check maximum position percentage
            max_position_value = account_value * self.max_position_pct
            if position_value > max_position_value:
                shares = int(max_position_value / entry_price)
                position_value = shares * entry_price
            
            # Ensure minimum of 1 share if any position
            if shares > 0:
                shares = max(1, shares)
            
            return shares
            
        except Exception as e:
            logger.error("❌ Error applying position limits", error=str(e))
            return 0
    
    async def _get_historical_performance(
        self,
        symbol: str,
        strategy: str
    ) -> Tuple[float, float, float]:
        """Get historical win rate and average win/loss for Kelly calculation."""
        try:
            # In production, this would query the database for historical trades
            # For demo, return reasonable estimates
            
            # Default values based on typical strategy performance
            default_performance = {
                'MomentumStrategy': (0.65, 0.08, 0.04),      # 65% win rate, 8% avg win, 4% avg loss
                'EarningsStrategy': (0.70, 0.12, 0.05),     # 70% win rate, 12% avg win, 5% avg loss
                'InstitutionalStrategy': (0.75, 0.15, 0.06) # 75% win rate, 15% avg win, 6% avg loss
            }
            
            return default_performance.get(strategy, (0.60, 0.06, 0.03))
            
        except Exception as e:
            logger.error("❌ Error getting historical performance", error=str(e))
            return 0.60, 0.06, 0.03  # Conservative defaults
    
    async def _estimate_volatility(self, symbol: str) -> float:
        """Estimate stock volatility for sizing adjustment."""
        try:
            # In production, you'd calculate actual volatility from price history
            # For demo, use sector-based estimates
            
            volatility_estimates = {
                # Technology stocks (higher volatility)
                'AAPL': 0.025, 'MSFT': 0.022, 'GOOGL': 0.028, 'AMZN': 0.030,
                'TSLA': 0.045, 'NVDA': 0.035, 'META': 0.032, 'NFLX': 0.038,
                
                # Financial stocks (moderate volatility)
                'JPM': 0.020, 'BAC': 0.022, 'WFC': 0.024, 'GS': 0.026,
                
                # Utilities (lower volatility)
                'NEE': 0.015, 'DUK': 0.014, 'SO': 0.013,
                
                # Default for unknown stocks
                'DEFAULT': 0.025
            }
            
            return volatility_estimates.get(symbol, volatility_estimates['DEFAULT'])
            
        except Exception as e:
            logger.error(f"❌ Error estimating volatility for {symbol}", error=str(e))
            return self.base_volatility
    
    async def _record_sizing_decision(
        self,
        symbol: str,
        shares: int,
        entry_price: float,
        account_value: float,
        risk_pct: float,
        confidence: float
    ) -> None:
        """Record position sizing decision for analysis."""
        try:
            sizing_record = {
                'timestamp': datetime.now(),
                'symbol': symbol,
                'shares': shares,
                'entry_price': entry_price,
                'position_value': shares * entry_price,
                'account_value': account_value,
                'position_pct': (shares * entry_price) / account_value,
                'risk_pct': risk_pct,
                'confidence': confidence,
                'sizing_method': self.sizing_method
            }
            
            self.sizing_history.append(sizing_record)
            
            # Keep only last 1000 records
            if len(self.sizing_history) > 1000:
                self.sizing_history = self.sizing_history[-1000:]
            
            logger.debug(f"📝 Sizing decision recorded", symbol=symbol, shares=shares)
            
        except Exception as e:
            logger.error("❌ Error recording sizing decision", error=str(e))
    
    def calculate_portfolio_heat(self, positions: List[Dict[str, Any]]) -> float:
        """Calculate portfolio heat based on position risks."""
        try:
            if not positions:
                return 0.0
            
            total_risk = 0.0
            total_value = 0.0
            
            for position in positions:
                position_value = abs(position.get('marketValue', 0))
                total_value += position_value
                
                # Estimate position risk (simplified)
                symbol = position.get('instrument', {}).get('symbol', '')
                volatility = asyncio.run(self._estimate_volatility(symbol))
                position_risk = position_value * volatility * 2  # 2 standard deviations
                
                total_risk += position_risk
            
            if total_value > 0:
                portfolio_heat = total_risk / total_value
            else:
                portfolio_heat = 0.0
            
            return min(1.0, portfolio_heat)  # Cap at 100%
            
        except Exception as e:
            logger.error("❌ Error calculating portfolio heat", error=str(e))
            return 0.0
    
    def get_sizing_stats(self) -> Dict[str, Any]:
        """Get position sizing statistics."""
        try:
            if not self.sizing_history:
                return {
                    'total_decisions': 0,
                    'avg_position_size': 0,
                    'avg_position_pct': 0,
                    'sizing_method': self.sizing_method
                }
            
            recent_decisions = self.sizing_history[-100:]  # Last 100 decisions
            
            avg_shares = np.mean([d['shares'] for d in recent_decisions])
            avg_value = np.mean([d['position_value'] for d in recent_decisions])
            avg_pct = np.mean([d['position_pct'] for d in recent_decisions])
            avg_risk = np.mean([d['risk_pct'] for d in recent_decisions])
            
            return {
                'total_decisions': len(self.sizing_history),
                'recent_decisions': len(recent_decisions),
                'avg_shares': avg_shares,
                'avg_position_value': avg_value,
                'avg_position_pct': avg_pct,
                'avg_risk_pct': avg_risk,
                'sizing_method': self.sizing_method,
                'min_position_value': self.min_position_value,
                'max_position_value': self.max_position_value,
                'max_position_pct': self.max_position_pct
            }
            
        except Exception as e:
            logger.error("❌ Error getting sizing stats", error=str(e))
            return {'error': str(e)}
    
    async def optimize_sizing_parameters(self) -> Dict[str, Any]:
        """Optimize sizing parameters based on historical performance."""
        try:
            if len(self.sizing_history) < 20:
                return {
                    'optimized': False,
                    'reason': 'Insufficient historical data'
                }
            
            # Analyze recent performance
            recent_decisions = self.sizing_history[-50:]
            
            # Calculate metrics by position size
            size_buckets = {
                'small': [d for d in recent_decisions if d['position_pct'] < 0.02],
                'medium': [d for d in recent_decisions if 0.02 <= d['position_pct'] < 0.04],
                'large': [d for d in recent_decisions if d['position_pct'] >= 0.04]
            }
            
            # Find optimal size bucket (this is simplified)
            best_bucket = 'medium'  # Default
            
            recommendations = {
                'current_method': self.sizing_method,
                'recommended_method': self.sizing_method,
                'optimal_risk_pct': self.default_risk_pct,
                'optimal_max_position': self.max_position_pct,
                'best_size_bucket': best_bucket,
                'optimization_date': datetime.now()
            }
            
            logger.info("🔧 Sizing parameters analyzed", recommendations=recommendations)
            
            return {
                'optimized': True,
                'recommendations': recommendations
            }
            
        except Exception as e:
            logger.error("❌ Error optimizing sizing parameters", error=str(e))
            return {'optimized': False, 'error': str(e)}
