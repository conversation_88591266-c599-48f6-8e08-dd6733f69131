"""
Health Check API Endpoint
Comprehensive system health monitoring for production deployment.

Author: Hector<PERSON>a1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from typing import Dict, Any
import structlog
import psutil
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.database import DatabaseManager
from utils.rate_limiter import RateLimiter

logger = structlog.get_logger(__name__)


async def health_check() -> Dict[str, Any]:
    """
    Comprehensive health check for all system components.
    
    Returns:
        Dict with health status, component details, and system metrics
    """
    health_data = {
        'timestamp': datetime.utcnow().isoformat(),
        'status': 'healthy',
        'version': '1.0.0',
        'environment': os.getenv('ENVIRONMENT', 'development'),
        'components': {},
        'system_metrics': {},
        'errors': []
    }
    
    try:
        # Check system resources
        health_data['system_metrics'] = await _check_system_resources()
        
        # Check database connectivity
        health_data['components']['database'] = await _check_database_health()
        
        # Check API rate limiters
        health_data['components']['rate_limiter'] = await _check_rate_limiter_health()
        
        # Check file system
        health_data['components']['filesystem'] = await _check_filesystem_health()
        
        # Check environment variables
        health_data['components']['environment'] = await _check_environment_variables()
        
        # Check external API connectivity (mock for demo)
        health_data['components']['external_apis'] = await _check_external_apis()
        
        # Determine overall health status
        component_statuses = [comp.get('status', 'unhealthy') for comp in health_data['components'].values()]
        
        if all(status == 'healthy' for status in component_statuses):
            health_data['status'] = 'healthy'
        elif any(status == 'critical' for status in component_statuses):
            health_data['status'] = 'critical'
        else:
            health_data['status'] = 'degraded'
        
        logger.info(
            "🔍 Health check completed",
            status=health_data['status'],
            components_checked=len(health_data['components'])
        )
        
        return health_data
        
    except Exception as e:
        logger.error("❌ Health check failed", error=str(e), exc_info=True)
        health_data['status'] = 'critical'
        health_data['errors'].append(f"Health check error: {str(e)}")
        return health_data


async def _check_system_resources() -> Dict[str, Any]:
    """Check system resource utilization."""
    try:
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Memory usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        memory_available_gb = memory.available / (1024**3)
        
        # Disk usage
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        disk_free_gb = disk.free / (1024**3)
        
        # Network statistics
        network = psutil.net_io_counters()
        
        return {
            'cpu_percent': round(cpu_percent, 1),
            'memory_percent': round(memory_percent, 1),
            'memory_available_gb': round(memory_available_gb, 2),
            'disk_percent': round(disk_percent, 1),
            'disk_free_gb': round(disk_free_gb, 2),
            'network_bytes_sent': network.bytes_sent,
            'network_bytes_recv': network.bytes_recv,
            'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0],
            'uptime_seconds': int(datetime.now().timestamp() - psutil.boot_time())
        }
        
    except Exception as e:
        logger.error("❌ Error checking system resources", error=str(e))
        return {'error': str(e)}


async def _check_database_health() -> Dict[str, Any]:
    """Check database connectivity and performance."""
    try:
        db_manager = DatabaseManager()
        
        # Test connection
        start_time = datetime.now()
        await db_manager.initialize()
        connection_time = (datetime.now() - start_time).total_seconds()
        
        # Get database stats
        db_stats = await db_manager.get_stats()
        
        await db_manager.close()
        
        return {
            'status': 'healthy',
            'connection_time_ms': round(connection_time * 1000, 2),
            'stats': db_stats,
            'last_check': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("❌ Database health check failed", error=str(e))
        return {
            'status': 'critical',
            'error': str(e),
            'last_check': datetime.utcnow().isoformat()
        }


async def _check_rate_limiter_health() -> Dict[str, Any]:
    """Check rate limiter health and statistics."""
    try:
        # Create test rate limiter
        rate_limiter = RateLimiter(max_calls=120, time_window=60)
        
        # Get health check
        health_check_result = await rate_limiter.health_check()
        
        return {
            'status': 'healthy' if health_check_result['healthy'] else 'degraded',
            'stats': health_check_result['stats'],
            'issues': health_check_result['issues'],
            'last_check': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("❌ Rate limiter health check failed", error=str(e))
        return {
            'status': 'critical',
            'error': str(e),
            'last_check': datetime.utcnow().isoformat()
        }


async def _check_filesystem_health() -> Dict[str, Any]:
    """Check filesystem health and permissions."""
    try:
        # Check critical directories
        critical_dirs = ['logs', 'data', 'backups']
        dir_status = {}
        
        for dir_name in critical_dirs:
            dir_path = Path(dir_name)
            dir_status[dir_name] = {
                'exists': dir_path.exists(),
                'writable': os.access(dir_path, os.W_OK) if dir_path.exists() else False,
                'size_mb': sum(f.stat().st_size for f in dir_path.rglob('*') if f.is_file()) / (1024**2) if dir_path.exists() else 0
            }
        
        # Check log file rotation
        log_files = list(Path('logs').glob('*.log')) if Path('logs').exists() else []
        
        return {
            'status': 'healthy',
            'directories': dir_status,
            'log_files_count': len(log_files),
            'total_log_size_mb': sum(f.stat().st_size for f in log_files) / (1024**2),
            'last_check': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("❌ Filesystem health check failed", error=str(e))
        return {
            'status': 'critical',
            'error': str(e),
            'last_check': datetime.utcnow().isoformat()
        }


async def _check_environment_variables() -> Dict[str, Any]:
    """Check required environment variables."""
    try:
        required_vars = [
            'SCHWAB_CLIENT_ID',
            'SCHWAB_CLIENT_SECRET',
            'TELEGRAM_BOT_TOKEN',
            'TELEGRAM_CHAT_ID'
        ]
        
        optional_vars = [
            'DATABASE_URL',
            'SENTRY_DSN',
            'LOG_LEVEL',
            'ENVIRONMENT'
        ]
        
        env_status = {
            'required': {},
            'optional': {},
            'missing_required': [],
            'missing_optional': []
        }
        
        # Check required variables
        for var in required_vars:
            value = os.getenv(var)
            env_status['required'][var] = bool(value)
            if not value:
                env_status['missing_required'].append(var)
        
        # Check optional variables
        for var in optional_vars:
            value = os.getenv(var)
            env_status['optional'][var] = bool(value)
            if not value:
                env_status['missing_optional'].append(var)
        
        # Determine status
        if env_status['missing_required']:
            status = 'critical'
        elif len(env_status['missing_optional']) > 2:
            status = 'degraded'
        else:
            status = 'healthy'
        
        return {
            'status': status,
            'details': env_status,
            'last_check': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("❌ Environment variables check failed", error=str(e))
        return {
            'status': 'critical',
            'error': str(e),
            'last_check': datetime.utcnow().isoformat()
        }


async def _check_external_apis() -> Dict[str, Any]:
    """Check external API connectivity."""
    try:
        api_checks = {
            'schwab_api': {
                'url': 'https://api.schwabapi.com',
                'status': 'unknown',
                'response_time_ms': 0
            },
            'telegram_api': {
                'url': 'https://api.telegram.org',
                'status': 'unknown',
                'response_time_ms': 0
            }
        }
        
        # In production, you'd make actual API calls to test connectivity
        # For demo, simulate API health
        for api_name, api_info in api_checks.items():
            api_info['status'] = 'healthy'
            api_info['response_time_ms'] = 150  # Simulated response time
        
        overall_status = 'healthy' if all(
            api['status'] == 'healthy' for api in api_checks.values()
        ) else 'degraded'
        
        return {
            'status': overall_status,
            'apis': api_checks,
            'last_check': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("❌ External API health check failed", error=str(e))
        return {
            'status': 'critical',
            'error': str(e),
            'last_check': datetime.utcnow().isoformat()
        }


# FastAPI/Flask endpoint wrapper
def create_health_endpoint():
    """Create health endpoint for web frameworks."""
    async def health_endpoint():
        """Health check endpoint."""
        try:
            health_data = await health_check()
            
            # Return appropriate HTTP status code
            if health_data['status'] == 'healthy':
                status_code = 200
            elif health_data['status'] == 'degraded':
                status_code = 200  # Still operational
            else:
                status_code = 503  # Service unavailable
            
            return {
                'status_code': status_code,
                'data': health_data
            }
            
        except Exception as e:
            logger.error("❌ Health endpoint error", error=str(e))
            return {
                'status_code': 503,
                'data': {
                    'status': 'critical',
                    'error': str(e),
                    'timestamp': datetime.utcnow().isoformat()
                }
            }
    
    return health_endpoint


# For serverless deployment (Vercel/Netlify)
async def handler(event, context):
    """Serverless function handler."""
    try:
        health_data = await health_check()
        
        return {
            'statusCode': 200 if health_data['status'] == 'healthy' else 503,
            'headers': {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache',
                'Access-Control-Allow-Origin': '*'
            },
            'body': json.dumps(health_data, default=str)
        }
        
    except Exception as e:
        return {
            'statusCode': 500,
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'status': 'error',
                'message': str(e),
                'timestamp': datetime.utcnow().isoformat()
            })
        }
