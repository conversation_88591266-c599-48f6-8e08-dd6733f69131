#!/usr/bin/env python3
"""
Import Test Script
Quick test to verify all imports work correctly.

Author: HectorTa1989
"""

import sys
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_imports():
    """Test all critical imports."""
    print("🧪 Testing imports...")
    
    try:
        # Test core modules
        print("  📦 Testing core modules...")
        from trading_bot import TradingBot
        print("    ✅ TradingBot imported successfully")
        
        from schwab_client import SchwabClient
        print("    ✅ SchwabClient imported successfully")
        
        from telegram_notifier import TelegramNotifier
        print("    ✅ TelegramNotifier imported successfully")
        
        # Test strategies
        print("  📦 Testing strategies...")
        from strategies.base_strategy import BaseStrategy
        print("    ✅ BaseStrategy imported successfully")
        
        from strategies.momentum_strategy import MomentumStrategy
        print("    ✅ MomentumStrategy imported successfully")
        
        from strategies.earnings_strategy import EarningsStrategy
        print("    ✅ EarningsStrategy imported successfully")
        
        from strategies.institutional_strategy import InstitutionalStrategy
        print("    ✅ InstitutionalStrategy imported successfully")
        
        # Test risk management
        print("  📦 Testing risk management...")
        from risk_management.risk_manager import RiskManager
        print("    ✅ RiskManager imported successfully")
        
        from risk_management.position_sizer import PositionSizer
        print("    ✅ PositionSizer imported successfully")
        
        # Test analysis
        print("  📦 Testing analysis modules...")
        from analysis.market_analyzer import MarketAnalyzer
        print("    ✅ MarketAnalyzer imported successfully")
        
        from analysis.technical_indicators import TechnicalIndicators
        print("    ✅ TechnicalIndicators imported successfully")
        
        # Test utilities
        print("  📦 Testing utilities...")
        from utils.database import DatabaseManager
        print("    ✅ DatabaseManager imported successfully")
        
        from utils.logger import setup_logging
        print("    ✅ Logger imported successfully")
        
        from utils.rate_limiter import RateLimiter
        print("    ✅ RateLimiter imported successfully")
        
        print("\n✅ All imports successful!")
        return True
        
    except ImportError as e:
        print(f"\n❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return False

def test_basic_initialization():
    """Test basic class initialization."""
    print("\n🔧 Testing basic initialization...")
    
    try:
        # Test configuration loading
        config_file = Path("config.json.template")
        if not config_file.exists():
            print("  ⚠️ config.json.template not found, skipping config test")
            return True
        
        # Test TradingBot initialization (without actual connections)
        from trading_bot import TradingBot
        
        # This should work even without credentials
        bot = TradingBot(config_file=str(config_file))
        print("    ✅ TradingBot initialized successfully")
        
        # Test strategy classes
        from strategies.momentum_strategy import MomentumStrategy
        from strategies.earnings_strategy import EarningsStrategy
        from strategies.institutional_strategy import InstitutionalStrategy
        
        # These should initialize with mock dependencies
        config = {"test": True}
        mock_client = None
        mock_analyzer = None
        mock_risk_manager = None
        
        momentum = MomentumStrategy(config, mock_client, mock_analyzer, mock_risk_manager)
        print("    ✅ MomentumStrategy initialized successfully")
        
        earnings = EarningsStrategy(config, mock_client, mock_analyzer, mock_risk_manager)
        print("    ✅ EarningsStrategy initialized successfully")
        
        institutional = InstitutionalStrategy(config, mock_client, mock_analyzer, mock_risk_manager)
        print("    ✅ InstitutionalStrategy initialized successfully")
        
        print("\n✅ Basic initialization successful!")
        return True
        
    except Exception as e:
        print(f"\n❌ Initialization error: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 Schwab Trading Bot - Import Test")
    print("=" * 50)
    
    # Test imports
    imports_ok = test_imports()
    
    # Test initialization
    init_ok = test_basic_initialization()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    if imports_ok and init_ok:
        print("✅ All tests passed! The application is ready to run.")
        print("\n🚀 Next steps:")
        print("  1. Copy .env.template to .env and add your credentials")
        print("  2. Copy config.json.template to config.json and customize")
        print("  3. Run: python main.py --test")
        return True
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("\n💡 Common issues:")
        print("  - Missing dependencies: pip install -r requirements.txt")
        print("  - Python path issues: ensure you're in the project root")
        print("  - Missing files: check that all source files exist")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
