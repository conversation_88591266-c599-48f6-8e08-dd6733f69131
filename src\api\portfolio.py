"""
Portfolio API Endpoint
Real-time portfolio data and analytics for dashboard integration.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import asyncio
import json
import os
import sys
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional
import structlog
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.database import DatabaseManager
from schwab_client import SchwabClient

logger = structlog.get_logger(__name__)


async def get_portfolio_data(
    include_positions: bool = True,
    include_performance: bool = True,
    include_analytics: bool = True
) -> Dict[str, Any]:
    """
    Get comprehensive portfolio data.
    
    Args:
        include_positions: Include current positions
        include_performance: Include performance metrics
        include_analytics: Include portfolio analytics
        
    Returns:
        Dict with portfolio data and metrics
    """
    try:
        portfolio_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'account_summary': {},
            'positions': [],
            'performance': {},
            'analytics': {},
            'risk_metrics': {}
        }
        
        # Initialize clients
        schwab_client = await _get_schwab_client()
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        # Get account summary
        portfolio_data['account_summary'] = await _get_account_summary(schwab_client)
        
        # Get positions if requested
        if include_positions:
            portfolio_data['positions'] = await _get_current_positions(schwab_client, db_manager)
        
        # Get performance metrics if requested
        if include_performance:
            portfolio_data['performance'] = await _get_performance_metrics(db_manager)
        
        # Get analytics if requested
        if include_analytics:
            portfolio_data['analytics'] = await _get_portfolio_analytics(
                portfolio_data['positions'],
                portfolio_data['account_summary']
            )
        
        # Calculate risk metrics
        portfolio_data['risk_metrics'] = await _calculate_risk_metrics(
            portfolio_data['positions'],
            portfolio_data['account_summary']
        )
        
        await db_manager.close()
        await schwab_client.close()
        
        logger.info("📊 Portfolio data retrieved successfully")
        return portfolio_data
        
    except Exception as e:
        logger.error("❌ Error getting portfolio data", error=str(e), exc_info=True)
        return {
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }


async def _get_schwab_client() -> SchwabClient:
    """Initialize Schwab client for API calls."""
    client = SchwabClient(
        client_id=os.getenv('SCHWAB_CLIENT_ID'),
        client_secret=os.getenv('SCHWAB_CLIENT_SECRET'),
        redirect_uri=os.getenv('SCHWAB_REDIRECT_URI'),
        account_number=os.getenv('SCHWAB_ACCOUNT_NUMBER')
    )
    await client.initialize()
    return client


async def _get_account_summary(schwab_client: SchwabClient) -> Dict[str, Any]:
    """Get account summary information."""
    try:
        account_info = await schwab_client.get_account_info()
        
        if not account_info:
            return {'error': 'Unable to retrieve account information'}
        
        # Extract key metrics
        summary = {
            'total_value': account_info.get('totalValue', 0),
            'cash_available': account_info.get('cashAvailable', 0),
            'buying_power': account_info.get('buyingPower', 0),
            'day_trading_buying_power': account_info.get('dayTradingBuyingPower', 0),
            'maintenance_requirement': account_info.get('maintenanceRequirement', 0),
            'long_market_value': account_info.get('longMarketValue', 0),
            'short_market_value': account_info.get('shortMarketValue', 0),
            'account_type': account_info.get('type', 'Unknown'),
            'last_updated': datetime.utcnow().isoformat()
        }
        
        # Calculate derived metrics
        summary['invested_percentage'] = (
            summary['long_market_value'] / max(summary['total_value'], 1) * 100
        )
        summary['cash_percentage'] = (
            summary['cash_available'] / max(summary['total_value'], 1) * 100
        )
        
        return summary
        
    except Exception as e:
        logger.error("❌ Error getting account summary", error=str(e))
        return {'error': str(e)}


async def _get_current_positions(
    schwab_client: SchwabClient,
    db_manager: DatabaseManager
) -> List[Dict[str, Any]]:
    """Get current positions with enhanced data."""
    try:
        # Get positions from Schwab
        positions = await schwab_client.get_positions()
        
        if not positions:
            return []
        
        enhanced_positions = []
        
        for position in positions:
            try:
                symbol = position.get('instrument', {}).get('symbol', '')
                if not symbol:
                    continue
                
                # Get current quote
                quotes = await schwab_client.get_quotes([symbol])
                current_quote = quotes.get(symbol, {})
                
                # Enhance position data
                enhanced_position = {
                    'symbol': symbol,
                    'quantity': position.get('longQuantity', 0) - position.get('shortQuantity', 0),
                    'market_value': position.get('marketValue', 0),
                    'average_price': position.get('averagePrice', 0),
                    'current_price': current_quote.get('last', 0),
                    'unrealized_pnl': position.get('unrealizedPnL', 0),
                    'unrealized_pnl_percent': position.get('unrealizedPnLPercent', 0),
                    'day_pnl': position.get('dayPnL', 0),
                    'day_pnl_percent': position.get('dayPnLPercent', 0),
                    'asset_type': position.get('instrument', {}).get('assetType', 'EQUITY'),
                    
                    # Additional market data
                    'bid': current_quote.get('bid', 0),
                    'ask': current_quote.get('ask', 0),
                    'volume': current_quote.get('volume', 0),
                    'change': current_quote.get('change', 0),
                    'change_percent': current_quote.get('changePercent', 0),
                    
                    # Risk metrics
                    'position_weight': 0,  # Will be calculated below
                    'risk_contribution': 0,
                    'beta': await _get_beta_estimate(symbol),
                    'volatility': await _get_volatility_estimate(symbol)
                }
                
                enhanced_positions.append(enhanced_position)
                
            except Exception as e:
                logger.error(f"❌ Error enhancing position data for {symbol}", error=str(e))
                continue
        
        # Calculate position weights and risk contributions
        total_value = sum(abs(pos['market_value']) for pos in enhanced_positions)
        
        for position in enhanced_positions:
            if total_value > 0:
                position['position_weight'] = abs(position['market_value']) / total_value * 100
                position['risk_contribution'] = (
                    position['position_weight'] * position['volatility'] / 100
                )
        
        # Sort by market value (largest first)
        enhanced_positions.sort(key=lambda x: abs(x['market_value']), reverse=True)
        
        return enhanced_positions
        
    except Exception as e:
        logger.error("❌ Error getting current positions", error=str(e))
        return []


async def _get_performance_metrics(db_manager: DatabaseManager) -> Dict[str, Any]:
    """Get portfolio performance metrics."""
    try:
        # Get recent trades
        end_date = datetime.utcnow()
        start_date = end_date - timedelta(days=30)
        
        recent_trades = await db_manager.get_trades(
            start_date=start_date,
            end_date=end_date,
            limit=1000
        )
        
        if not recent_trades:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'avg_return': 0,
                'total_pnl': 0
            }
        
        # Calculate performance metrics
        total_trades = len(recent_trades)
        winning_trades = sum(1 for trade in recent_trades if trade.get('pnl', 0) > 0)
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        total_pnl = sum(trade.get('pnl', 0) for trade in recent_trades)
        avg_return = total_pnl / total_trades if total_trades > 0 else 0
        
        # Calculate by strategy
        strategy_performance = {}
        for trade in recent_trades:
            strategy = trade.get('strategy', 'Unknown')
            if strategy not in strategy_performance:
                strategy_performance[strategy] = {
                    'trades': 0,
                    'wins': 0,
                    'total_pnl': 0
                }
            
            strategy_performance[strategy]['trades'] += 1
            if trade.get('pnl', 0) > 0:
                strategy_performance[strategy]['wins'] += 1
            strategy_performance[strategy]['total_pnl'] += trade.get('pnl', 0)
        
        # Calculate strategy win rates
        for strategy, data in strategy_performance.items():
            data['win_rate'] = (data['wins'] / data['trades'] * 100) if data['trades'] > 0 else 0
            data['avg_return'] = data['total_pnl'] / data['trades'] if data['trades'] > 0 else 0
        
        return {
            'period_days': 30,
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': total_trades - winning_trades,
            'win_rate': round(win_rate, 1),
            'total_pnl': round(total_pnl, 2),
            'avg_return': round(avg_return, 2),
            'strategy_performance': strategy_performance,
            'last_updated': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("❌ Error getting performance metrics", error=str(e))
        return {'error': str(e)}


async def _get_portfolio_analytics(
    positions: List[Dict],
    account_summary: Dict
) -> Dict[str, Any]:
    """Get advanced portfolio analytics."""
    try:
        if not positions:
            return {'error': 'No positions for analysis'}
        
        # Sector allocation
        sector_allocation = await _calculate_sector_allocation(positions)
        
        # Concentration analysis
        concentration_metrics = await _calculate_concentration_metrics(positions)
        
        # Risk metrics
        portfolio_risk = await _calculate_portfolio_risk(positions)
        
        # Performance attribution
        performance_attribution = await _calculate_performance_attribution(positions)
        
        return {
            'sector_allocation': sector_allocation,
            'concentration_metrics': concentration_metrics,
            'portfolio_risk': portfolio_risk,
            'performance_attribution': performance_attribution,
            'diversification_score': await _calculate_diversification_score(positions),
            'sharpe_ratio': await _calculate_sharpe_ratio(positions),
            'last_updated': datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("❌ Error getting portfolio analytics", error=str(e))
        return {'error': str(e)}


async def _get_beta_estimate(symbol: str) -> float:
    """Get beta estimate for a symbol."""
    # Simplified beta estimates by sector
    beta_estimates = {
        'AAPL': 1.2, 'MSFT': 0.9, 'GOOGL': 1.1, 'AMZN': 1.3, 'TSLA': 2.0,
        'NVDA': 1.8, 'META': 1.4, 'NFLX': 1.5, 'AMD': 1.7, 'CRM': 1.3
    }
    return beta_estimates.get(symbol, 1.0)


async def _get_volatility_estimate(symbol: str) -> float:
    """Get volatility estimate for a symbol."""
    # Simplified volatility estimates (annualized %)
    volatility_estimates = {
        'AAPL': 25, 'MSFT': 22, 'GOOGL': 28, 'AMZN': 30, 'TSLA': 45,
        'NVDA': 35, 'META': 32, 'NFLX': 38, 'AMD': 40, 'CRM': 35
    }
    return volatility_estimates.get(symbol, 25)


# Serverless handler
async def handler(event, context):
    """Serverless function handler for portfolio endpoint."""
    try:
        # Parse query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        
        include_positions = query_params.get('positions', 'true').lower() == 'true'
        include_performance = query_params.get('performance', 'true').lower() == 'true'
        include_analytics = query_params.get('analytics', 'true').lower() == 'true'
        
        # Get portfolio data
        portfolio_data = await get_portfolio_data(
            include_positions=include_positions,
            include_performance=include_performance,
            include_analytics=include_analytics
        )
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps(portfolio_data, default=str)
        }
        
    except Exception as e:
        logger.error("❌ Portfolio endpoint error", error=str(e))
        return {
            'statusCode': 500,
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            })
        }
