#!/usr/bin/env python3
"""
Project Validation Script
Validates project structure, files, and configuration without running the full application.

Author: HectorTa1989
"""

import os
import json
from pathlib import Path
from typing import List, Dict, Any


def validate_project_structure() -> bool:
    """Validate that all required files and directories exist."""
    print("🔍 Validating project structure...")
    
    required_files = [
        'main.py',
        'requirements.txt',
        'config.json.template',
        '.env.template',
        'README.md',
        'Dockerfile',
        'src/trading_bot.py',
        'src/schwab_client.py',
        'src/telegram_notifier.py',
        'src/strategies/base_strategy.py',
        'src/strategies/momentum_strategy.py',
        'src/strategies/earnings_strategy.py',
        'src/strategies/institutional_strategy.py',
        'src/risk_management/risk_manager.py',
        'src/risk_management/position_sizer.py',
        'src/analysis/market_analyzer.py',
        'src/analysis/technical_indicators.py',
        'src/utils/database.py',
        'src/utils/logger.py',
        'src/utils/rate_limiter.py',
        'src/api/health.py',
        'src/api/portfolio.py',
        'src/api/trades.py',
        'src/api/metrics.py',
        'tests/test_schwab_client.py',
        'tests/test_trading_bot.py',
        'tests/test_strategies.py',
        'docs/API.md',
        'docs/DEPLOYMENT.md',
        'docs/STRATEGIES.md',
        'scripts/setup.py',
        'scripts/run_tests.py',
        'templates/dashboard.html'
    ]
    
    required_directories = [
        'src',
        'src/strategies',
        'src/risk_management',
        'src/analysis',
        'src/utils',
        'src/api',
        'tests',
        'docs',
        'scripts',
        'templates'
    ]
    
    missing_files = []
    missing_dirs = []
    
    # Check directories
    for directory in required_directories:
        if not Path(directory).exists():
            missing_dirs.append(directory)
        else:
            print(f"  ✅ Directory: {directory}")
    
    # Check files
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
        else:
            print(f"  ✅ File: {file_path}")
    
    # Report results
    if missing_dirs:
        print(f"\n❌ Missing directories: {missing_dirs}")
    
    if missing_files:
        print(f"\n❌ Missing files: {missing_files}")
    
    if not missing_dirs and not missing_files:
        print("\n✅ All required files and directories present!")
        return True
    else:
        print(f"\n❌ Project structure incomplete: {len(missing_dirs)} missing dirs, {len(missing_files)} missing files")
        return False


def validate_configuration_templates() -> bool:
    """Validate configuration template files."""
    print("\n🔧 Validating configuration templates...")
    
    try:
        # Check config.json.template
        config_template = Path('config.json.template')
        if config_template.exists():
            with open(config_template, 'r') as f:
                config_data = json.load(f)
            
            required_sections = ['schwab', 'telegram', 'trading', 'strategies', 'risk_management']
            
            for section in required_sections:
                if section in config_data:
                    print(f"  ✅ Config section: {section}")
                else:
                    print(f"  ❌ Missing config section: {section}")
                    return False
        else:
            print("  ❌ config.json.template not found")
            return False
        
        # Check .env.template
        env_template = Path('.env.template')
        if env_template.exists():
            with open(env_template, 'r') as f:
                env_content = f.read()
            
            required_vars = ['SCHWAB_CLIENT_ID', 'SCHWAB_CLIENT_SECRET', 'TELEGRAM_BOT_TOKEN', 'TELEGRAM_CHAT_ID']
            
            for var in required_vars:
                if var in env_content:
                    print(f"  ✅ Environment variable: {var}")
                else:
                    print(f"  ❌ Missing environment variable: {var}")
                    return False
        else:
            print("  ❌ .env.template not found")
            return False
        
        print("\n✅ Configuration templates are valid!")
        return True
        
    except json.JSONDecodeError as e:
        print(f"\n❌ Invalid JSON in config template: {e}")
        return False
    except Exception as e:
        print(f"\n❌ Error validating configuration: {e}")
        return False


def validate_dependencies() -> bool:
    """Validate requirements.txt and dependencies."""
    print("\n📦 Validating dependencies...")
    
    try:
        requirements_file = Path('requirements.txt')
        if not requirements_file.exists():
            print("  ❌ requirements.txt not found")
            return False
        
        with open(requirements_file, 'r') as f:
            requirements = f.read().strip().split('\n')
        
        # Check for critical dependencies
        critical_deps = [
            'aiohttp',
            'structlog',
            'asyncio',
            'pandas',
            'numpy'
        ]
        
        found_deps = []
        for req in requirements:
            if req.strip() and not req.startswith('#'):
                dep_name = req.split('>=')[0].split('==')[0].split('[')[0].strip()
                found_deps.append(dep_name)
        
        for dep in critical_deps:
            if any(dep in found_dep for found_dep in found_deps):
                print(f"  ✅ Dependency: {dep}")
            else:
                print(f"  ⚠️ Missing critical dependency: {dep}")
        
        print(f"\n✅ Found {len(found_deps)} dependencies in requirements.txt")
        return True
        
    except Exception as e:
        print(f"\n❌ Error validating dependencies: {e}")
        return False


def validate_docker_setup() -> bool:
    """Validate Docker configuration."""
    print("\n🐳 Validating Docker setup...")
    
    try:
        dockerfile = Path('Dockerfile')
        if dockerfile.exists():
            print("  ✅ Dockerfile present")
        else:
            print("  ❌ Dockerfile missing")
            return False
        
        # Check for docker-compose if it exists
        docker_compose = Path('docker-compose.yml')
        if docker_compose.exists():
            print("  ✅ docker-compose.yml present")
        else:
            print("  ℹ️ docker-compose.yml not found (optional)")
        
        print("\n✅ Docker setup validated!")
        return True
        
    except Exception as e:
        print(f"\n❌ Error validating Docker setup: {e}")
        return False


def generate_project_report() -> Dict[str, Any]:
    """Generate comprehensive project validation report."""
    print("\n📊 Generating project report...")
    
    report = {
        'timestamp': str(Path.cwd()),
        'validation_time': str(Path('main.py').stat().st_mtime if Path('main.py').exists() else 'unknown'),
        'project_structure': 'valid',
        'configuration': 'valid',
        'dependencies': 'valid',
        'docker_setup': 'valid',
        'total_files': len(list(Path('.').rglob('*.py'))),
        'total_size_mb': sum(f.stat().st_size for f in Path('.').rglob('*') if f.is_file()) / (1024 * 1024),
        'recommendations': []
    }
    
    # Add recommendations
    if not Path('.env').exists():
        report['recommendations'].append('Create .env file from .env.template')
    
    if not Path('config.json').exists():
        report['recommendations'].append('Create config.json from config.json.template')
    
    if not Path('logs').exists():
        report['recommendations'].append('Create logs directory: mkdir logs')
    
    if not Path('data').exists():
        report['recommendations'].append('Create data directory: mkdir data')
    
    print(f"  📁 Total Python files: {report['total_files']}")
    print(f"  📏 Project size: {report['total_size_mb']:.1f} MB")
    print(f"  💡 Recommendations: {len(report['recommendations'])}")
    
    return report


def main():
    """Main validation function."""
    print("🚀 Schwab Trading Bot - Project Validation")
    print("=" * 60)
    
    # Run all validations
    structure_ok = validate_project_structure()
    config_ok = validate_configuration_templates()
    deps_ok = validate_dependencies()
    docker_ok = validate_docker_setup()
    
    # Generate report
    report = generate_project_report()
    
    # Final summary
    print("\n" + "=" * 60)
    print("📋 VALIDATION SUMMARY")
    print("=" * 60)
    
    all_valid = structure_ok and config_ok and deps_ok and docker_ok
    
    if all_valid:
        print("✅ PROJECT VALIDATION PASSED")
        print("\n🎯 The project is properly structured and ready for deployment!")
        print("\n🚀 Next steps:")
        print("  1. Install dependencies: pip install -r requirements.txt")
        print("  2. Setup environment: python scripts/setup.py")
        print("  3. Configure credentials in .env and config.json")
        print("  4. Test the application: python main.py --test")
        print("  5. Deploy to your preferred platform")
        
        if report['recommendations']:
            print("\n💡 Recommendations:")
            for rec in report['recommendations']:
                print(f"  - {rec}")
    else:
        print("❌ PROJECT VALIDATION FAILED")
        print("\n🔧 Please fix the issues above before proceeding.")
    
    print(f"\n📊 Project Statistics:")
    print(f"  - Python files: {report['total_files']}")
    print(f"  - Project size: {report['total_size_mb']:.1f} MB")
    print(f"  - Validation time: {report['validation_time']}")
    
    return all_valid


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
