# Deployment Guide

## Overview

This guide covers deploying the Schwab Telegram Trading Bot across different platforms, from local development to production cloud environments.

## Prerequisites

- Python 3.9+ installed
- Docker (for containerized deployment)
- Git for version control
- Valid Schwab API credentials
- Telegram bot token

## Local Development

### 1. Environment Setup

```bash
# Clone repository
git clone https://github.com/HectorTa1989/schwab-telegram-trading-bot.git
cd schwab-telegram-trading-bot

# Run automated setup
python scripts/setup.py --dev

# Or manual setup
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows

pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy templates
cp .env.template .env
cp config.json.template config.json

# Edit with your credentials
nano .env
nano config.json
```

### 3. Database Setup

```bash
# For development (SQLite)
mkdir -p data
# Database will be created automatically

# For production (PostgreSQL)
createdb schwab_trading_bot
psql schwab_trading_bot < scripts/schema.sql
```

### 4. Run Application

```bash
# Test mode
python main.py --test

# Paper trading
python main.py

# Live trading (after testing)
python main.py --live
```

## Docker Deployment

### 1. Build Image

```bash
# Build production image
docker build -t schwab-trading-bot:latest .

# Build with specific tag
docker build -t schwab-trading-bot:v1.0.0 .
```

### 2. Run Container

```bash
# Run with environment file
docker run -d \
  --name schwab-trading-bot \
  --env-file .env \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/logs:/app/logs \
  -p 8000:8000 \
  schwab-trading-bot:latest

# Run with docker-compose
docker-compose up -d
```

### 3. Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  trading-bot:
    build: .
    container_name: schwab-trading-bot
    restart: unless-stopped
    env_file: .env
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./backups:/app/backups
    ports:
      - "8000:8000"
      - "8080:8080"
    depends_on:
      - postgres
      - redis
    
  postgres:
    image: postgres:15-alpine
    container_name: schwab-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: schwab_trading_bot
      POSTGRES_USER: trading_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/schema.sql:/docker-entrypoint-initdb.d/schema.sql
    ports:
      - "5432:5432"
  
  redis:
    image: redis:7-alpine
    container_name: schwab-redis
    restart: unless-stopped
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

## Cloud Deployment

### AWS Deployment

#### 1. ECS (Elastic Container Service)

```bash
# Build and push to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 123456789012.dkr.ecr.us-east-1.amazonaws.com

docker build -t schwab-trading-bot .
docker tag schwab-trading-bot:latest 123456789012.dkr.ecr.us-east-1.amazonaws.com/schwab-trading-bot:latest
docker push 123456789012.dkr.ecr.us-east-1.amazonaws.com/schwab-trading-bot:latest

# Deploy with ECS CLI
ecs-cli compose --file docker-compose.yml up --create-log-groups
```

#### 2. Lambda (Serverless)

```bash
# Install serverless framework
npm install -g serverless

# Deploy serverless functions
serverless deploy --stage production

# Or use AWS SAM
sam build
sam deploy --guided
```

#### 3. EC2 Instance

```bash
# Launch EC2 instance with user data script
#!/bin/bash
yum update -y
yum install -y docker git

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Clone and run application
git clone https://github.com/HectorTa1989/schwab-telegram-trading-bot.git
cd schwab-telegram-trading-bot

# Setup environment (you'd use AWS Secrets Manager in production)
echo "SCHWAB_CLIENT_ID=${SCHWAB_CLIENT_ID}" > .env
echo "SCHWAB_CLIENT_SECRET=${SCHWAB_CLIENT_SECRET}" >> .env
echo "TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}" >> .env

docker-compose up -d
```

### Google Cloud Platform

#### 1. Cloud Run

```bash
# Build and push to Container Registry
gcloud builds submit --tag gcr.io/PROJECT_ID/schwab-trading-bot

# Deploy to Cloud Run
gcloud run deploy schwab-trading-bot \
  --image gcr.io/PROJECT_ID/schwab-trading-bot \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars ENVIRONMENT=production
```

#### 2. Compute Engine

```bash
# Create instance with container
gcloud compute instances create-with-container schwab-trading-bot \
  --container-image gcr.io/PROJECT_ID/schwab-trading-bot \
  --machine-type e2-medium \
  --zone us-central1-a
```

### Azure Deployment

#### 1. Container Instances

```bash
# Create resource group
az group create --name schwab-trading-bot --location eastus

# Deploy container
az container create \
  --resource-group schwab-trading-bot \
  --name schwab-trading-bot \
  --image schwab-trading-bot:latest \
  --cpu 1 \
  --memory 2 \
  --restart-policy Always \
  --environment-variables ENVIRONMENT=production
```

### Netlify (Serverless Functions)

```bash
# Install Netlify CLI
npm install -g netlify-cli

# Deploy
netlify deploy --prod

# Or connect GitHub repository for automatic deployments
```

### Vercel (Edge Functions)

```bash
# Install Vercel CLI
npm install -g vercel

# Deploy
vercel --prod

# Or connect GitHub repository
```

## Production Considerations

### 1. Security

```bash
# Use secrets management
# AWS Secrets Manager
aws secretsmanager create-secret \
  --name schwab-trading-bot/credentials \
  --secret-string '{"schwab_client_id":"xxx","schwab_client_secret":"xxx"}'

# Google Secret Manager
gcloud secrets create schwab-credentials --data-file=credentials.json

# Azure Key Vault
az keyvault secret set \
  --vault-name schwab-keyvault \
  --name schwab-client-id \
  --value "your_client_id"
```

### 2. Monitoring

```bash
# Setup monitoring with Prometheus
docker run -d \
  --name prometheus \
  -p 9090:9090 \
  -v $(pwd)/prometheus.yml:/etc/prometheus/prometheus.yml \
  prom/prometheus

# Setup Grafana dashboard
docker run -d \
  --name grafana \
  -p 3000:3000 \
  grafana/grafana
```

### 3. Logging

```bash
# Centralized logging with ELK stack
docker run -d \
  --name elasticsearch \
  -p 9200:9200 \
  -e "discovery.type=single-node" \
  elasticsearch:7.17.0

docker run -d \
  --name kibana \
  -p 5601:5601 \
  --link elasticsearch:elasticsearch \
  kibana:7.17.0
```

### 4. Backup Strategy

```bash
# Database backups
pg_dump schwab_trading_bot > backup_$(date +%Y%m%d).sql

# Automated backups with cron
0 2 * * * /usr/local/bin/backup_script.sh

# Cloud storage backup
aws s3 sync ./backups s3://schwab-trading-bot-backups/
```

## Scaling

### Horizontal Scaling

```yaml
# Kubernetes deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: schwab-trading-bot
spec:
  replicas: 3
  selector:
    matchLabels:
      app: schwab-trading-bot
  template:
    metadata:
      labels:
        app: schwab-trading-bot
    spec:
      containers:
      - name: trading-bot
        image: schwab-trading-bot:latest
        ports:
        - containerPort: 8000
        env:
        - name: ENVIRONMENT
          value: "production"
```

### Load Balancing

```nginx
# Nginx configuration
upstream trading_bot {
    server 127.0.0.1:8000;
    server 127.0.0.1:8001;
    server 127.0.0.1:8002;
}

server {
    listen 80;
    server_name schwab-trading-bot.com;
    
    location / {
        proxy_pass http://trading_bot;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   ```bash
   # Check token file
   cat .schwab_tokens.json
   
   # Regenerate tokens
   rm .schwab_tokens.json
   python main.py --authenticate
   ```

2. **Database Connection Issues**
   ```bash
   # Test database connection
   python -c "from src.utils.database import DatabaseManager; import asyncio; asyncio.run(DatabaseManager().initialize())"
   ```

3. **Rate Limiting**
   ```bash
   # Check rate limiter stats
   curl http://localhost:8000/api/health | jq '.components.rate_limiter'
   ```

### Logs Analysis

```bash
# View real-time logs
tail -f logs/trading_bot.log

# Search for errors
grep "ERROR" logs/trading_bot.log

# Analyze performance
grep "performance_metric" logs/performance.log | jq .
```

## Maintenance

### Regular Tasks

1. **Daily**: Check logs and performance metrics
2. **Weekly**: Review trading performance and adjust strategies
3. **Monthly**: Update dependencies and security patches
4. **Quarterly**: Full system audit and optimization

### Updates

```bash
# Update application
git pull origin main
pip install -r requirements.txt --upgrade
python main.py --test  # Test before deploying

# Rolling update with Docker
docker-compose pull
docker-compose up -d --no-deps trading-bot
```

## Support

For deployment support:
- 📧 Email: <EMAIL>
- 💬 Discord: https://discord.gg/schwab-trading-bot
- 📖 Wiki: https://github.com/HectorTa1989/schwab-telegram-trading-bot/wiki
