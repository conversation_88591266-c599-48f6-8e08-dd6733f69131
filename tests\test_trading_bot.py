"""
Test Suite for Main Trading Bot
Integration tests for the core trading bot functionality.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import pytest
import asyncio
import json
import tempfile
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from pathlib import Path

# Import test configuration
from . import TEST_CONFIG

# Import the module to test
from src.trading_bot import TradingBot


class TestTradingBot:
    """Test suite for main trading bot."""
    
    @pytest.fixture
    async def trading_bot(self):
        """Create a test trading bot instance."""
        # Create temporary config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(TEST_CONFIG, f)
            config_file = f.name
        
        bot = TradingBot(config_file=config_file)
        
        # Mock external dependencies
        bot.schwab_client = AsyncMock()
        bot.telegram_notifier = AsyncMock()
        bot.database_manager = AsyncMock()
        bot.risk_manager = AsyncMock()
        bot.market_analyzer = AsyncMock()
        
        yield bot
        
        # Cleanup
        Path(config_file).unlink(missing_ok=True)
        if hasattr(bot, 'cleanup'):
            await bot.cleanup()
    
    @pytest.mark.asyncio
    async def test_initialization(self, trading_bot):
        """Test trading bot initialization."""
        assert trading_bot.config is not None
        assert trading_bot.is_running is False
        assert trading_bot.paper_trading is True  # Should default to paper trading
        assert len(trading_bot.strategies) == 0  # No strategies loaded yet
    
    @pytest.mark.asyncio
    async def test_load_strategies(self, trading_bot):
        """Test strategy loading."""
        # Mock strategy configuration
        trading_bot.config['strategies'] = {
            'momentum_breakout': {'enabled': True},
            'earnings_momentum': {'enabled': False}
        }
        
        await trading_bot._load_strategies()
        
        # Should load enabled strategies only
        assert len(trading_bot.strategies) >= 1
        assert any(strategy.name == 'MomentumStrategy' for strategy in trading_bot.strategies)
    
    @pytest.mark.asyncio
    async def test_signal_generation(self, trading_bot):
        """Test signal generation process."""
        # Setup mock strategy
        mock_strategy = AsyncMock()
        mock_strategy.name = 'TestStrategy'
        mock_strategy.is_enabled = True
        mock_strategy.generate_signals.return_value = [
            {
                'symbol': 'AAPL',
                'side': 'BUY',
                'entry_price': 150.00,
                'confidence': 0.8,
                'strategy': 'TestStrategy'
            }
        ]
        
        trading_bot.strategies = [mock_strategy]
        
        # Test signal generation
        signals = await trading_bot._generate_signals()
        
        assert len(signals) == 1
        assert signals[0]['symbol'] == 'AAPL'
        assert signals[0]['side'] == 'BUY'
        mock_strategy.generate_signals.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_signal_validation(self, trading_bot):
        """Test signal validation process."""
        # Mock risk manager validation
        trading_bot.risk_manager.validate_trade.return_value = {
            'approved': True,
            'reason': 'All checks passed',
            'position_size': 10
        }
        
        test_signal = {
            'symbol': 'AAPL',
            'side': 'BUY',
            'entry_price': 150.00,
            'confidence': 0.8
        }
        
        # Test validation
        is_valid, reason, position_size = await trading_bot._validate_signal(test_signal)
        
        assert is_valid is True
        assert reason == 'All checks passed'
        assert position_size == 10
        trading_bot.risk_manager.validate_trade.assert_called_once_with(test_signal)
    
    @pytest.mark.asyncio
    async def test_order_execution(self, trading_bot):
        """Test order execution process."""
        # Mock successful order execution
        trading_bot.schwab_client.place_order.return_value = {
            'success': True,
            'order_id': 'test_order_123',
            'status': 'PENDING'
        }
        
        test_signal = {
            'symbol': 'AAPL',
            'side': 'BUY',
            'entry_price': 150.00,
            'quantity': 10,
            'order_type': 'LIMIT'
        }
        
        # Test order execution
        result = await trading_bot._execute_order(test_signal)
        
        assert result['success'] is True
        assert result['order_id'] == 'test_order_123'
        trading_bot.schwab_client.place_order.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_position_monitoring(self, trading_bot):
        """Test position monitoring and exit logic."""
        # Mock current positions
        trading_bot.schwab_client.get_positions.return_value = [
            {
                'instrument': {'symbol': 'AAPL'},
                'longQuantity': 10,
                'marketValue': 1500.00,
                'averagePrice': 145.00,
                'unrealizedPnL': 50.00
            }
        ]
        
        # Mock strategy exit decision
        mock_strategy = AsyncMock()
        mock_strategy.should_exit_position.return_value = (True, "Take profit reached")
        trading_bot.strategies = [mock_strategy]
        
        # Test position monitoring
        exit_signals = await trading_bot._monitor_positions()
        
        assert len(exit_signals) >= 0  # May or may not generate exit signals
        trading_bot.schwab_client.get_positions.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_error_handling(self, trading_bot):
        """Test error handling in main loop."""
        # Mock an exception in signal generation
        mock_strategy = AsyncMock()
        mock_strategy.generate_signals.side_effect = Exception("Test error")
        trading_bot.strategies = [mock_strategy]
        
        # Test that errors are handled gracefully
        signals = await trading_bot._generate_signals()
        
        # Should return empty list on error, not crash
        assert signals == []
    
    @pytest.mark.asyncio
    async def test_paper_trading_mode(self, trading_bot):
        """Test paper trading mode functionality."""
        trading_bot.paper_trading = True
        
        test_signal = {
            'symbol': 'AAPL',
            'side': 'BUY',
            'entry_price': 150.00,
            'quantity': 10
        }
        
        # In paper trading mode, orders should be simulated
        result = await trading_bot._execute_order(test_signal)
        
        # Should simulate execution without calling real API
        assert 'simulated' in result or not trading_bot.schwab_client.place_order.called
    
    @pytest.mark.asyncio
    async def test_notification_sending(self, trading_bot):
        """Test notification sending."""
        test_message = "Test trading notification"
        
        # Test notification
        await trading_bot._send_notification(test_message, priority='normal')
        
        # Should call telegram notifier
        trading_bot.telegram_notifier.send_message.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_performance_tracking(self, trading_bot):
        """Test performance metrics tracking."""
        # Mock performance data
        trading_bot.database_manager.get_trades.return_value = [
            {
                'symbol': 'AAPL',
                'pnl_amount': 50.00,
                'pnl_percent': 3.33,
                'strategy': 'TestStrategy'
            }
        ]
        
        # Test performance calculation
        performance = await trading_bot._calculate_performance_metrics()
        
        assert 'total_pnl' in performance
        assert 'win_rate' in performance
        trading_bot.database_manager.get_trades.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_market_hours_check(self, trading_bot):
        """Test market hours validation."""
        # Test during market hours (simplified)
        current_time = datetime.now().replace(hour=14, minute=30)  # 2:30 PM
        
        with patch('datetime.datetime') as mock_datetime:
            mock_datetime.now.return_value = current_time
            mock_datetime.side_effect = lambda *args, **kw: datetime(*args, **kw)
            
            is_market_open = trading_bot._is_market_open()
            
            # Should be True for weekday afternoon
            assert isinstance(is_market_open, bool)
    
    @pytest.mark.asyncio
    async def test_configuration_validation(self, trading_bot):
        """Test configuration validation."""
        # Test with invalid configuration
        invalid_config = {
            'schwab': {},  # Missing required fields
            'telegram': {},
            'trading': {}
        }
        
        with pytest.raises(Exception):
            await trading_bot._validate_configuration(invalid_config)
        
        # Test with valid configuration
        valid_config = TEST_CONFIG
        
        # Should not raise exception
        await trading_bot._validate_configuration(valid_config)
    
    @pytest.mark.asyncio
    async def test_graceful_shutdown(self, trading_bot):
        """Test graceful shutdown process."""
        trading_bot.is_running = True
        
        # Test shutdown
        await trading_bot.shutdown()
        
        assert trading_bot.is_running is False
        
        # Should close all connections
        if trading_bot.schwab_client:
            trading_bot.schwab_client.close.assert_called_once()


@pytest.mark.asyncio
async def test_trading_bot_integration():
    """Integration test for trading bot (requires configuration)."""
    # Skip if no configuration file available
    config_file = 'config.json'
    if not Path(config_file).exists():
        pytest.skip("No config.json file available for integration test")
    
    try:
        bot = TradingBot(config_file=config_file)
        
        # Test initialization
        await bot.initialize()
        
        # Test basic functionality
        if bot.paper_trading:
            # Run one cycle in paper trading mode
            await bot._run_trading_cycle()
        
        # Test shutdown
        await bot.shutdown()
        
    except Exception as e:
        pytest.skip(f"Integration test failed: {e}")


if __name__ == '__main__':
    # Run tests
    pytest.main([__file__, '-v'])
