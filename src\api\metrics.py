"""
Metrics API Endpoint
Performance metrics, analytics, and monitoring data for dashboard and alerts.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import asyncio
import json
import os
import sys
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional
import structlog
from pathlib import Path
import psutil

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.database import DatabaseManager
from utils.rate_limiter import RateLimiter
from schwab_client import SchwabClient

logger = structlog.get_logger(__name__)


async def get_metrics_data(
    metric_type: str = 'all',
    time_range: str = '24h',
    include_system: bool = True
) -> Dict[str, Any]:
    """
    Get comprehensive metrics and monitoring data.
    
    Args:
        metric_type: Type of metrics ('trading', 'system', 'performance', 'all')
        time_range: Time range ('1h', '24h', '7d', '30d')
        include_system: Include system metrics
        
    Returns:
        Dict with metrics data
    """
    try:
        metrics_data = {
            'timestamp': datetime.utcnow().isoformat(),
            'time_range': time_range,
            'metric_type': metric_type,
            'trading_metrics': {},
            'system_metrics': {},
            'performance_metrics': {},
            'alerts': []
        }
        
        # Parse time range
        time_delta = _parse_time_range(time_range)
        start_time = datetime.utcnow() - time_delta
        
        # Get trading metrics
        if metric_type in ['trading', 'all']:
            metrics_data['trading_metrics'] = await _get_trading_metrics(start_time)
        
        # Get system metrics
        if metric_type in ['system', 'all'] and include_system:
            metrics_data['system_metrics'] = await _get_system_metrics()
        
        # Get performance metrics
        if metric_type in ['performance', 'all']:
            metrics_data['performance_metrics'] = await _get_performance_metrics(start_time)
        
        # Get alerts and warnings
        metrics_data['alerts'] = await _get_active_alerts()
        
        logger.info(
            "📊 Metrics data retrieved",
            metric_type=metric_type,
            time_range=time_range
        )
        
        return metrics_data
        
    except Exception as e:
        logger.error("❌ Error getting metrics data", error=str(e), exc_info=True)
        return {
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }


async def _get_trading_metrics(start_time: datetime) -> Dict[str, Any]:
    """Get trading-specific metrics."""
    try:
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        # Get trades in time range
        trades = await db_manager.get_trades(
            start_date=start_time,
            end_date=datetime.utcnow(),
            limit=1000
        )
        
        # Get current positions
        schwab_client = await _get_schwab_client()
        positions = await schwab_client.get_positions()
        account_info = await schwab_client.get_account_info()
        
        # Calculate metrics
        trading_metrics = {
            'trades_count': len(trades),
            'positions_count': len(positions) if positions else 0,
            'total_portfolio_value': account_info.get('totalValue', 0) if account_info else 0,
            'cash_available': account_info.get('cashAvailable', 0) if account_info else 0,
            'buying_power': account_info.get('buyingPower', 0) if account_info else 0,
            'day_pnl': sum(pos.get('dayPnL', 0) for pos in positions) if positions else 0,
            'unrealized_pnl': sum(pos.get('unrealizedPnL', 0) for pos in positions) if positions else 0,
        }
        
        # Calculate trade frequency
        if trades:
            time_span_hours = (datetime.utcnow() - start_time).total_seconds() / 3600
            trading_metrics['trades_per_hour'] = len(trades) / max(time_span_hours, 1)
            
            # Strategy distribution
            strategy_counts = {}
            for trade in trades:
                strategy = trade.get('strategy', 'Unknown')
                strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
            
            trading_metrics['strategy_distribution'] = strategy_counts
        
        await db_manager.close()
        await schwab_client.close()
        
        return trading_metrics
        
    except Exception as e:
        logger.error("❌ Error getting trading metrics", error=str(e))
        return {'error': str(e)}


async def _get_system_metrics() -> Dict[str, Any]:
    """Get system performance metrics."""
    try:
        # CPU and Memory
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Network statistics
        network = psutil.net_io_counters()
        
        # Process information
        process = psutil.Process()
        process_memory = process.memory_info()
        
        # Application uptime
        boot_time = psutil.boot_time()
        uptime_seconds = datetime.now().timestamp() - boot_time
        
        system_metrics = {
            'cpu_percent': round(cpu_percent, 1),
            'memory_percent': round(memory.percent, 1),
            'memory_used_gb': round(memory.used / (1024**3), 2),
            'memory_available_gb': round(memory.available / (1024**3), 2),
            'disk_percent': round((disk.used / disk.total) * 100, 1),
            'disk_free_gb': round(disk.free / (1024**3), 2),
            'network_bytes_sent': network.bytes_sent,
            'network_bytes_recv': network.bytes_recv,
            'process_memory_mb': round(process_memory.rss / (1024**2), 2),
            'process_cpu_percent': round(process.cpu_percent(), 1),
            'uptime_hours': round(uptime_seconds / 3600, 1),
            'load_average': list(os.getloadavg()) if hasattr(os, 'getloadavg') else [0, 0, 0]
        }
        
        # Rate limiter statistics
        rate_limiter = RateLimiter(max_calls=120, time_window=60)
        rate_stats = rate_limiter.get_stats()
        system_metrics['rate_limiter'] = rate_stats
        
        return system_metrics
        
    except Exception as e:
        logger.error("❌ Error getting system metrics", error=str(e))
        return {'error': str(e)}


async def _get_performance_metrics(start_time: datetime) -> Dict[str, Any]:
    """Get performance and profitability metrics."""
    try:
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        # Get completed trades for performance calculation
        trades = await db_manager.get_trades(
            start_date=start_time,
            end_date=datetime.utcnow(),
            limit=1000
        )
        
        completed_trades = [t for t in trades if t.get('status') == 'FILLED' and 'pnl_amount' in t]
        
        if not completed_trades:
            await db_manager.close()
            return {
                'total_trades': 0,
                'total_pnl': 0,
                'win_rate': 0,
                'sharpe_ratio': 0,
                'max_drawdown': 0
            }
        
        # Calculate performance metrics
        pnl_amounts = [t['pnl_amount'] for t in completed_trades]
        returns = [t.get('pnl_percent', 0) / 100 for t in completed_trades]
        
        total_pnl = sum(pnl_amounts)
        winning_trades = sum(1 for pnl in pnl_amounts if pnl > 0)
        win_rate = (winning_trades / len(completed_trades)) * 100
        
        # Calculate Sharpe ratio (simplified)
        if len(returns) > 1:
            avg_return = np.mean(returns)
            return_std = np.std(returns)
            sharpe_ratio = (avg_return / return_std) * sqrt(252) if return_std > 0 else 0
        else:
            sharpe_ratio = 0
        
        # Calculate maximum drawdown
        cumulative_returns = np.cumsum(returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = cumulative_returns - running_max
        max_drawdown = abs(min(drawdowns)) if len(drawdowns) > 0 else 0
        
        # Calculate additional metrics
        avg_win = np.mean([pnl for pnl in pnl_amounts if pnl > 0]) if winning_trades > 0 else 0
        avg_loss = np.mean([pnl for pnl in pnl_amounts if pnl < 0]) if len(pnl_amounts) - winning_trades > 0 else 0
        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else 0
        
        # Calculate Sortino ratio (downside deviation)
        negative_returns = [r for r in returns if r < 0]
        if negative_returns:
            downside_std = np.std(negative_returns)
            sortino_ratio = (np.mean(returns) / downside_std) * sqrt(252) if downside_std > 0 else 0
        else:
            sortino_ratio = sharpe_ratio
        
        performance_metrics = {
            'total_trades': len(completed_trades),
            'winning_trades': winning_trades,
            'losing_trades': len(completed_trades) - winning_trades,
            'win_rate': round(win_rate, 1),
            'total_pnl': round(total_pnl, 2),
            'avg_win': round(avg_win, 2),
            'avg_loss': round(avg_loss, 2),
            'profit_factor': round(profit_factor, 2),
            'sharpe_ratio': round(sharpe_ratio, 2),
            'sortino_ratio': round(sortino_ratio, 2),
            'max_drawdown': round(max_drawdown * 100, 2),  # Convert to percentage
            'avg_return': round(np.mean(returns) * 100, 2),
            'return_std': round(np.std(returns) * 100, 2),
            'best_trade': round(max(pnl_amounts), 2),
            'worst_trade': round(min(pnl_amounts), 2)
        }
        
        await db_manager.close()
        return performance_metrics
        
    except Exception as e:
        logger.error("❌ Error getting performance metrics", error=str(e))
        return {'error': str(e)}


async def _get_active_alerts() -> List[Dict[str, Any]]:
    """Get active alerts and warnings."""
    try:
        alerts = []
        
        # System resource alerts
        cpu_percent = psutil.cpu_percent()
        memory_percent = psutil.virtual_memory().percent
        disk_percent = psutil.disk_usage('/').percent
        
        if cpu_percent > 80:
            alerts.append({
                'type': 'system',
                'severity': 'warning',
                'message': f'High CPU usage: {cpu_percent:.1f}%',
                'timestamp': datetime.utcnow().isoformat()
            })
        
        if memory_percent > 85:
            alerts.append({
                'type': 'system',
                'severity': 'warning',
                'message': f'High memory usage: {memory_percent:.1f}%',
                'timestamp': datetime.utcnow().isoformat()
            })
        
        if disk_percent > 90:
            alerts.append({
                'type': 'system',
                'severity': 'critical',
                'message': f'Low disk space: {disk_percent:.1f}% used',
                'timestamp': datetime.utcnow().isoformat()
            })
        
        # Trading alerts (would be populated by risk manager)
        # For demo, add sample alerts
        
        return alerts
        
    except Exception as e:
        logger.error("❌ Error getting active alerts", error=str(e))
        return []


def _parse_time_range(time_range: str) -> timedelta:
    """Parse time range string to timedelta."""
    time_ranges = {
        '1h': timedelta(hours=1),
        '24h': timedelta(hours=24),
        '7d': timedelta(days=7),
        '30d': timedelta(days=30),
        '90d': timedelta(days=90)
    }
    
    return time_ranges.get(time_range, timedelta(hours=24))


async def _get_schwab_client() -> SchwabClient:
    """Initialize Schwab client for API calls."""
    client = SchwabClient(
        client_id=os.getenv('SCHWAB_CLIENT_ID'),
        client_secret=os.getenv('SCHWAB_CLIENT_SECRET'),
        redirect_uri=os.getenv('SCHWAB_REDIRECT_URI'),
        account_number=os.getenv('SCHWAB_ACCOUNT_NUMBER')
    )
    await client.initialize()
    return client


# Serverless handler
async def handler(event, context):
    """Serverless function handler for metrics endpoint."""
    try:
        # Parse query parameters
        query_params = event.get('queryStringParameters', {}) or {}
        
        metrics_data = await get_metrics_data(
            metric_type=query_params.get('type', 'all'),
            time_range=query_params.get('range', '24h'),
            include_system=query_params.get('system', 'true').lower() == 'true'
        )
        
        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'GET, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            },
            'body': json.dumps(metrics_data, default=str)
        }
        
    except Exception as e:
        logger.error("❌ Metrics endpoint error", error=str(e))
        return {
            'statusCode': 500,
            'headers': {'Content-Type': 'application/json'},
            'body': json.dumps({
                'error': str(e),
                'timestamp': datetime.utcnow().isoformat()
            })
        }
