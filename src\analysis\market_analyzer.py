"""
Market Analyzer
Comprehensive market analysis with sentiment, sector rotation, and macro indicators.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import structlog
import aiohttp
import numpy as np
from technical_indicators import TechnicalIndicators

logger = structlog.get_logger(__name__)


class MarketAnalyzer:
    """
    Comprehensive market analysis engine with multiple data sources and indicators.
    
    Features:
    - Market sentiment analysis
    - Sector rotation tracking
    - VIX and fear/greed indicators
    - Economic calendar integration
    - News sentiment analysis
    - Technical market structure analysis
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize market analyzer."""
        self.config = config
        self.technical_indicators = TechnicalIndicators()
        
        # Market data configuration
        self.update_frequency = config.get('update_frequency', 60)  # seconds
        self.data_retention_days = config.get('data_retention_days', 30)
        
        # Market indices to track
        self.market_indices = {
            'SPY': 'S&P 500',
            'QQQ': 'NASDAQ 100', 
            'IWM': 'Russell 2000',
            'VIX': 'Volatility Index',
            'DXY': 'US Dollar Index',
            'TLT': '20+ Year Treasury',
            'GLD': 'Gold',
            'USO': 'Oil'
        }
        
        # Sector ETFs for rotation analysis
        self.sector_etfs = {
            'XLK': 'Technology',
            'XLF': 'Financials',
            'XLV': 'Healthcare',
            'XLE': 'Energy',
            'XLI': 'Industrials',
            'XLY': 'Consumer Discretionary',
            'XLP': 'Consumer Staples',
            'XLU': 'Utilities',
            'XLB': 'Materials',
            'XLRE': 'Real Estate',
            'XLC': 'Communication Services'
        }
        
        # Market data cache
        self.market_data_cache: Dict[str, Dict] = {}
        self.sentiment_data: Dict[str, Any] = {}
        self.sector_performance: Dict[str, Dict] = {}
        self.last_update = None
        
        # Market regime detection
        self.current_regime = 'neutral'  # bull, bear, neutral, volatile
        self.regime_confidence = 0.5
        
        logger.info(
            "✅ Market analyzer initialized",
            indices_tracked=len(self.market_indices),
            sectors_tracked=len(self.sector_etfs),
            update_frequency=self.update_frequency
        )
    
    async def analyze_market_conditions(self) -> Dict[str, Any]:
        """Comprehensive market condition analysis."""
        try:
            # Update market data if needed
            await self._update_market_data()
            
            # Analyze different aspects
            sentiment_analysis = await self._analyze_market_sentiment()
            sector_analysis = await self._analyze_sector_rotation()
            volatility_analysis = await self._analyze_volatility_regime()
            technical_analysis = await self._analyze_market_technicals()
            
            # Combine into overall market assessment
            market_conditions = {
                'timestamp': datetime.now(),
                'overall_sentiment': sentiment_analysis['overall_sentiment'],
                'sentiment_score': sentiment_analysis['sentiment_score'],
                'market_regime': self.current_regime,
                'regime_confidence': self.regime_confidence,
                'vix_level': volatility_analysis['vix_level'],
                'vix_regime': volatility_analysis['regime'],
                'leading_sectors': sector_analysis['leading_sectors'][:3],
                'lagging_sectors': sector_analysis['lagging_sectors'][:3],
                'market_breadth': technical_analysis['breadth_score'],
                'trend_strength': technical_analysis['trend_strength'],
                'support_levels': technical_analysis['support_levels'],
                'resistance_levels': technical_analysis['resistance_levels'],
                'trading_recommendation': await self._generate_trading_recommendation(
                    sentiment_analysis, sector_analysis, volatility_analysis, technical_analysis
                )
            }
            
            logger.info(
                "📊 Market analysis completed",
                sentiment=market_conditions['overall_sentiment'],
                regime=self.current_regime,
                vix=volatility_analysis['vix_level']
            )
            
            return market_conditions
            
        except Exception as e:
            logger.error("❌ Error analyzing market conditions", error=str(e), exc_info=True)
            return self._get_default_market_conditions()
    
    async def _update_market_data(self) -> None:
        """Update market data cache."""
        try:
            # Check if update is needed
            if (self.last_update and 
                datetime.now() - self.last_update < timedelta(seconds=self.update_frequency)):
                return
            
            # In production, you'd fetch real market data
            # For demo, simulate market data
            await self._simulate_market_data()
            
            self.last_update = datetime.now()
            
        except Exception as e:
            logger.error("❌ Error updating market data", error=str(e))
    
    async def _simulate_market_data(self) -> None:
        """Simulate market data for demonstration."""
        # Simulate current market conditions
        self.market_data_cache = {
            'SPY': {'last': 485.50, 'change': 2.15, 'change_pct': 0.44, 'volume': 45000000},
            'QQQ': {'last': 415.25, 'change': 3.80, 'change_pct': 0.92, 'volume': 35000000},
            'IWM': {'last': 198.75, 'change': -0.85, 'change_pct': -0.43, 'volume': 25000000},
            'VIX': {'last': 18.45, 'change': -1.25, 'change_pct': -6.35, 'volume': 0},
            'DXY': {'last': 103.25, 'change': 0.15, 'change_pct': 0.15, 'volume': 0}
        }
        
        # Simulate sector performance
        self.sector_performance = {
            'XLK': {'performance_1d': 1.2, 'performance_5d': 3.8, 'performance_20d': 8.5},
            'XLF': {'performance_1d': 0.8, 'performance_5d': 2.1, 'performance_20d': 5.2},
            'XLV': {'performance_1d': 0.3, 'performance_5d': 1.5, 'performance_20d': 3.8},
            'XLE': {'performance_1d': -0.5, 'performance_5d': -1.2, 'performance_20d': 2.1},
            'XLI': {'performance_1d': 0.6, 'performance_5d': 2.8, 'performance_20d': 6.3}
        }
    
    async def _analyze_market_sentiment(self) -> Dict[str, Any]:
        """Analyze overall market sentiment."""
        try:
            # Get key market indicators
            spy_data = self.market_data_cache.get('SPY', {})
            vix_data = self.market_data_cache.get('VIX', {})
            
            spy_change = spy_data.get('change_pct', 0)
            vix_level = vix_data.get('last', 20)
            
            # Calculate sentiment score
            sentiment_score = 0.5  # Neutral baseline
            
            # SPY performance component (40% weight)
            if spy_change > 1:
                sentiment_score += 0.2  # Very positive
            elif spy_change > 0:
                sentiment_score += 0.1  # Positive
            elif spy_change < -1:
                sentiment_score -= 0.2  # Very negative
            elif spy_change < 0:
                sentiment_score -= 0.1  # Negative
            
            # VIX component (30% weight)
            if vix_level < 15:
                sentiment_score += 0.15  # Low fear
            elif vix_level < 20:
                sentiment_score += 0.05  # Normal
            elif vix_level > 30:
                sentiment_score -= 0.15  # High fear
            elif vix_level > 25:
                sentiment_score -= 0.05  # Elevated fear
            
            # Market breadth component (30% weight)
            breadth_score = await self._calculate_market_breadth()
            sentiment_score += (breadth_score - 0.5) * 0.3
            
            # Normalize sentiment score
            sentiment_score = max(0, min(1, sentiment_score))
            
            # Determine overall sentiment
            if sentiment_score > 0.7:
                overall_sentiment = 'bullish'
            elif sentiment_score > 0.6:
                overall_sentiment = 'moderately_bullish'
            elif sentiment_score > 0.4:
                overall_sentiment = 'neutral'
            elif sentiment_score > 0.3:
                overall_sentiment = 'moderately_bearish'
            else:
                overall_sentiment = 'bearish'
            
            return {
                'overall_sentiment': overall_sentiment,
                'sentiment_score': sentiment_score,
                'spy_performance': spy_change,
                'vix_level': vix_level,
                'market_breadth': breadth_score,
                'fear_greed_index': self._calculate_fear_greed_index()
            }
            
        except Exception as e:
            logger.error("❌ Error analyzing market sentiment", error=str(e))
            return {
                'overall_sentiment': 'neutral',
                'sentiment_score': 0.5,
                'error': str(e)
            }
    
    async def _analyze_sector_rotation(self) -> Dict[str, Any]:
        """Analyze sector rotation patterns."""
        try:
            # Calculate sector performance rankings
            sector_rankings = []
            
            for etf, sector_name in self.sector_etfs.items():
                performance = self.sector_performance.get(etf, {})
                
                # Weight recent performance more heavily
                score = (
                    performance.get('performance_1d', 0) * 0.5 +
                    performance.get('performance_5d', 0) * 0.3 +
                    performance.get('performance_20d', 0) * 0.2
                )
                
                sector_rankings.append({
                    'sector': sector_name,
                    'etf': etf,
                    'score': score,
                    'performance_1d': performance.get('performance_1d', 0),
                    'performance_5d': performance.get('performance_5d', 0),
                    'performance_20d': performance.get('performance_20d', 0)
                })
            
            # Sort by performance score
            sector_rankings.sort(key=lambda x: x['score'], reverse=True)
            
            # Identify rotation patterns
            leading_sectors = sector_rankings[:3]
            lagging_sectors = sector_rankings[-3:]
            
            # Determine rotation theme
            rotation_theme = await self._identify_rotation_theme(sector_rankings)
            
            return {
                'leading_sectors': leading_sectors,
                'lagging_sectors': lagging_sectors,
                'rotation_theme': rotation_theme,
                'sector_rankings': sector_rankings,
                'rotation_strength': self._calculate_rotation_strength(sector_rankings)
            }
            
        except Exception as e:
            logger.error("❌ Error analyzing sector rotation", error=str(e))
            return {
                'leading_sectors': [],
                'lagging_sectors': [],
                'rotation_theme': 'unclear'
            }
    
    async def _analyze_volatility_regime(self) -> Dict[str, Any]:
        """Analyze volatility regime and market stress."""
        try:
            vix_data = self.market_data_cache.get('VIX', {})
            vix_level = vix_data.get('last', 20)
            vix_change = vix_data.get('change_pct', 0)
            
            # Determine volatility regime
            if vix_level < 15:
                regime = 'low_volatility'
                stress_level = 'low'
            elif vix_level < 20:
                regime = 'normal_volatility'
                stress_level = 'normal'
            elif vix_level < 30:
                regime = 'elevated_volatility'
                stress_level = 'elevated'
            else:
                regime = 'high_volatility'
                stress_level = 'high'
            
            # Check for volatility spikes
            volatility_spike = vix_change > 20  # 20% VIX increase
            
            return {
                'vix_level': vix_level,
                'vix_change': vix_change,
                'regime': regime,
                'stress_level': stress_level,
                'volatility_spike': volatility_spike,
                'trading_recommendation': self._get_volatility_trading_recommendation(regime)
            }
            
        except Exception as e:
            logger.error("❌ Error analyzing volatility regime", error=str(e))
            return {
                'vix_level': 20,
                'regime': 'normal_volatility',
                'stress_level': 'normal'
            }
    
    async def _analyze_market_technicals(self) -> Dict[str, Any]:
        """Analyze market technical structure."""
        try:
            spy_data = self.market_data_cache.get('SPY', {})
            spy_price = spy_data.get('last', 0)
            
            # Calculate technical indicators for SPY
            indicators = await self.technical_indicators.calculate_all_indicators(
                'SPY', spy_price
            )
            
            # Market breadth analysis
            breadth_score = await self._calculate_market_breadth()
            
            # Trend strength analysis
            trend_strength = await self._calculate_trend_strength(indicators)
            
            # Support and resistance levels
            support_levels = await self._calculate_support_levels(spy_price)
            resistance_levels = await self._calculate_resistance_levels(spy_price)
            
            return {
                'breadth_score': breadth_score,
                'trend_strength': trend_strength,
                'support_levels': support_levels,
                'resistance_levels': resistance_levels,
                'technical_indicators': indicators,
                'market_structure': await self._assess_market_structure(indicators)
            }
            
        except Exception as e:
            logger.error("❌ Error analyzing market technicals", error=str(e))
            return {
                'breadth_score': 0.5,
                'trend_strength': 0.5,
                'support_levels': [],
                'resistance_levels': []
            }
    
    async def _calculate_market_breadth(self) -> float:
        """Calculate market breadth score."""
        try:
            # In production, you'd calculate advance/decline ratio, new highs/lows, etc.
            # For demo, simulate based on index performance
            
            spy_change = self.market_data_cache.get('SPY', {}).get('change_pct', 0)
            qqq_change = self.market_data_cache.get('QQQ', {}).get('change_pct', 0)
            iwm_change = self.market_data_cache.get('IWM', {}).get('change_pct', 0)
            
            # Simple breadth calculation
            positive_indices = sum(1 for change in [spy_change, qqq_change, iwm_change] if change > 0)
            breadth_score = positive_indices / 3
            
            return breadth_score
            
        except Exception as e:
            logger.error("❌ Error calculating market breadth", error=str(e))
            return 0.5
    
    async def _calculate_trend_strength(self, indicators: Dict[str, float]) -> float:
        """Calculate trend strength based on technical indicators."""
        try:
            # Use multiple indicators to assess trend strength
            rsi = indicators.get('rsi', 50)
            macd = indicators.get('macd', 0)
            sma_20 = indicators.get('sma_20', 0)
            sma_50 = indicators.get('sma_50', 0)
            
            spy_price = self.market_data_cache.get('SPY', {}).get('last', 0)
            
            strength_score = 0.5  # Neutral baseline
            
            # RSI component
            if rsi > 60:
                strength_score += 0.1
            elif rsi < 40:
                strength_score -= 0.1
            
            # MACD component
            if macd > 0:
                strength_score += 0.1
            else:
                strength_score -= 0.1
            
            # Moving average component
            if spy_price > sma_20 > sma_50:
                strength_score += 0.2  # Strong uptrend
            elif spy_price < sma_20 < sma_50:
                strength_score -= 0.2  # Strong downtrend
            
            return max(0, min(1, strength_score))
            
        except Exception as e:
            logger.error("❌ Error calculating trend strength", error=str(e))
            return 0.5
    
    async def _calculate_support_levels(self, current_price: float) -> List[float]:
        """Calculate key support levels."""
        try:
            # Simplified support calculation
            # In production, you'd use pivot points, volume profile, etc.
            
            support_levels = [
                current_price * 0.98,  # 2% below
                current_price * 0.95,  # 5% below
                current_price * 0.90   # 10% below
            ]
            
            return sorted(support_levels, reverse=True)
            
        except Exception as e:
            logger.error("❌ Error calculating support levels", error=str(e))
            return []
    
    async def _calculate_resistance_levels(self, current_price: float) -> List[float]:
        """Calculate key resistance levels."""
        try:
            # Simplified resistance calculation
            resistance_levels = [
                current_price * 1.02,  # 2% above
                current_price * 1.05,  # 5% above
                current_price * 1.10   # 10% above
            ]
            
            return sorted(resistance_levels)
            
        except Exception as e:
            logger.error("❌ Error calculating resistance levels", error=str(e))
            return []
    
    async def _identify_rotation_theme(self, sector_rankings: List[Dict]) -> str:
        """Identify the current sector rotation theme."""
        try:
            # Get top 3 performing sectors
            top_sectors = [s['sector'] for s in sector_rankings[:3]]
            
            # Identify common rotation themes
            if 'Technology' in top_sectors and 'Communication Services' in top_sectors:
                return 'growth_rotation'
            elif 'Financials' in top_sectors and 'Energy' in top_sectors:
                return 'value_rotation'
            elif 'Utilities' in top_sectors and 'Consumer Staples' in top_sectors:
                return 'defensive_rotation'
            elif 'Consumer Discretionary' in top_sectors:
                return 'consumer_strength'
            else:
                return 'mixed_rotation'
                
        except Exception as e:
            logger.error("❌ Error identifying rotation theme", error=str(e))
            return 'unclear'
    
    def _calculate_rotation_strength(self, sector_rankings: List[Dict]) -> float:
        """Calculate the strength of sector rotation."""
        try:
            if len(sector_rankings) < 2:
                return 0.0
            
            # Calculate spread between best and worst performing sectors
            best_performance = sector_rankings[0]['score']
            worst_performance = sector_rankings[-1]['score']
            
            rotation_strength = abs(best_performance - worst_performance) / 10  # Normalize
            return min(1.0, rotation_strength)
            
        except Exception as e:
            logger.error("❌ Error calculating rotation strength", error=str(e))
            return 0.0
    
    def _calculate_fear_greed_index(self) -> float:
        """Calculate simplified fear/greed index."""
        try:
            # Simplified calculation based on VIX and market performance
            vix_level = self.market_data_cache.get('VIX', {}).get('last', 20)
            spy_change = self.market_data_cache.get('SPY', {}).get('change_pct', 0)
            
            # VIX component (inverted - lower VIX = more greed)
            vix_component = max(0, min(1, (35 - vix_level) / 20))
            
            # Market performance component
            performance_component = max(0, min(1, (spy_change + 2) / 4))
            
            # Combine components
            fear_greed = (vix_component + performance_component) / 2
            
            return fear_greed
            
        except Exception as e:
            logger.error("❌ Error calculating fear/greed index", error=str(e))
            return 0.5
    
    async def _generate_trading_recommendation(
        self,
        sentiment: Dict,
        sector: Dict,
        volatility: Dict,
        technical: Dict
    ) -> Dict[str, Any]:
        """Generate overall trading recommendation."""
        try:
            sentiment_score = sentiment.get('sentiment_score', 0.5)
            vix_level = volatility.get('vix_level', 20)
            trend_strength = technical.get('trend_strength', 0.5)
            
            # Calculate overall score
            overall_score = (sentiment_score + trend_strength) / 2
            
            # Adjust for volatility
            if vix_level > 25:
                overall_score *= 0.8  # Reduce aggressiveness in high volatility
            
            # Generate recommendation
            if overall_score > 0.7 and vix_level < 25:
                recommendation = 'aggressive_long'
                position_sizing = 'normal'
            elif overall_score > 0.6:
                recommendation = 'moderate_long'
                position_sizing = 'reduced'
            elif overall_score > 0.4:
                recommendation = 'neutral'
                position_sizing = 'minimal'
            elif overall_score > 0.3:
                recommendation = 'defensive'
                position_sizing = 'minimal'
            else:
                recommendation = 'risk_off'
                position_sizing = 'none'
            
            return {
                'recommendation': recommendation,
                'position_sizing': position_sizing,
                'overall_score': overall_score,
                'confidence': abs(overall_score - 0.5) * 2  # Distance from neutral
            }
            
        except Exception as e:
            logger.error("❌ Error generating trading recommendation", error=str(e))
            return {
                'recommendation': 'neutral',
                'position_sizing': 'minimal',
                'overall_score': 0.5
            }
    
    def _get_default_market_conditions(self) -> Dict[str, Any]:
        """Get default market conditions in case of errors."""
        return {
            'timestamp': datetime.now(),
            'overall_sentiment': 'neutral',
            'sentiment_score': 0.5,
            'market_regime': 'neutral',
            'regime_confidence': 0.5,
            'vix_level': 20.0,
            'vix_regime': 'normal_volatility',
            'leading_sectors': [],
            'lagging_sectors': [],
            'market_breadth': 0.5,
            'trend_strength': 0.5,
            'support_levels': [],
            'resistance_levels': [],
            'trading_recommendation': {
                'recommendation': 'neutral',
                'position_sizing': 'minimal',
                'overall_score': 0.5
            }
        }

    async def _assess_market_structure(self, indicators: Dict[str, float]) -> Dict[str, Any]:
        """Assess overall market structure and health."""
        try:
            structure_score = 0.5  # Neutral baseline

            # Moving average alignment
            sma_20 = indicators.get('sma_20', 0)
            sma_50 = indicators.get('sma_50', 0)
            sma_200 = indicators.get('sma_200', 0)

            if sma_20 > sma_50 > sma_200:
                structure_score += 0.2  # Strong bullish structure
            elif sma_20 < sma_50 < sma_200:
                structure_score -= 0.2  # Strong bearish structure

            # Volume confirmation
            volume_ratio = indicators.get('volume_ratio', 1.0)
            if volume_ratio > 1.2:
                structure_score += 0.1  # Good volume support
            elif volume_ratio < 0.8:
                structure_score -= 0.1  # Weak volume

            # Volatility assessment
            bb_width = indicators.get('bb_width', 10)
            if bb_width < 5:
                structure_score -= 0.1  # Low volatility (potential breakout)
            elif bb_width > 20:
                structure_score -= 0.1  # High volatility (unstable)

            structure_score = max(0, min(1, structure_score))

            # Determine structure quality
            if structure_score > 0.7:
                structure_quality = 'strong'
            elif structure_score > 0.6:
                structure_quality = 'good'
            elif structure_score > 0.4:
                structure_quality = 'neutral'
            elif structure_score > 0.3:
                structure_quality = 'weak'
            else:
                structure_quality = 'poor'

            return {
                'structure_score': structure_score,
                'structure_quality': structure_quality,
                'ma_alignment': 'bullish' if sma_20 > sma_50 > sma_200 else 'bearish' if sma_20 < sma_50 < sma_200 else 'mixed',
                'volume_confirmation': volume_ratio > 1.0,
                'volatility_regime': 'low' if bb_width < 10 else 'high' if bb_width > 20 else 'normal'
            }

        except Exception as e:
            logger.error("❌ Error assessing market structure", error=str(e))
            return {
                'structure_score': 0.5,
                'structure_quality': 'neutral',
                'ma_alignment': 'mixed'
            }

    def _get_volatility_trading_recommendation(self, regime: str) -> str:
        """Get trading recommendation based on volatility regime."""
        recommendations = {
            'low_volatility': 'Increase position sizes, expect breakout',
            'normal_volatility': 'Normal position sizing, standard strategies',
            'elevated_volatility': 'Reduce position sizes, tighter stops',
            'high_volatility': 'Minimal positions, defensive strategies'
        }

        return recommendations.get(regime, 'Monitor closely')

    async def get_market_summary(self) -> Dict[str, Any]:
        """Get concise market summary for notifications."""
        try:
            conditions = await self.analyze_market_conditions()

            return {
                'sentiment': conditions['overall_sentiment'],
                'regime': conditions['market_regime'],
                'vix': conditions['vix_level'],
                'leading_sector': conditions['leading_sectors'][0]['sector'] if conditions['leading_sectors'] else 'N/A',
                'recommendation': conditions['trading_recommendation']['recommendation'],
                'timestamp': datetime.now().strftime('%H:%M EST')
            }

        except Exception as e:
            logger.error("❌ Error getting market summary", error=str(e))
            return {
                'sentiment': 'neutral',
                'regime': 'neutral',
                'vix': 20.0,
                'leading_sector': 'N/A',
                'recommendation': 'neutral'
            }
