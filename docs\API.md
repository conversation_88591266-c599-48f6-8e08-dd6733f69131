# API Documentation

## Overview

The Schwab Telegram Trading Bot provides a comprehensive RESTful API for monitoring, controlling, and analyzing trading operations. All endpoints support JSON responses and include proper error handling.

## Base URL

- **Development**: `http://localhost:8000/api`
- **Production**: `https://your-domain.com/api`

## Authentication

All API endpoints require authentication via API key or JWT token:

```bash
curl -H "Authorization: Bearer YOUR_API_TOKEN" \
     -H "Content-Type: application/json" \
     https://your-domain.com/api/portfolio
```

## Endpoints

### Health Check

**GET** `/health`

Check system health and component status.

**Response:**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "status": "healthy",
  "version": "1.0.0",
  "environment": "production",
  "components": {
    "database": {
      "status": "healthy",
      "connection_time_ms": 45.2,
      "stats": {
        "trades_count": 1250,
        "positions_count": 8
      }
    },
    "rate_limiter": {
      "status": "healthy",
      "utilization": 35.5,
      "requests_remaining": 78
    }
  },
  "system_metrics": {
    "cpu_percent": 15.2,
    "memory_percent": 45.8,
    "disk_free_gb": 125.4
  }
}
```

### Portfolio Data

**GET** `/portfolio`

Get comprehensive portfolio information.

**Query Parameters:**
- `positions` (boolean): Include current positions (default: true)
- `performance` (boolean): Include performance metrics (default: true)
- `analytics` (boolean): Include portfolio analytics (default: true)

**Response:**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "account_summary": {
    "total_value": 125000.50,
    "cash_available": 25000.00,
    "buying_power": 50000.00,
    "invested_percentage": 80.0,
    "cash_percentage": 20.0
  },
  "positions": [
    {
      "symbol": "AAPL",
      "quantity": 100,
      "market_value": 15025.00,
      "average_price": 145.50,
      "current_price": 150.25,
      "unrealized_pnl": 475.00,
      "unrealized_pnl_percent": 3.26,
      "position_weight": 12.02,
      "beta": 1.2,
      "volatility": 25.0
    }
  ],
  "performance": {
    "total_trades": 45,
    "win_rate": 68.9,
    "total_pnl": 5250.75,
    "avg_return": 116.68
  },
  "analytics": {
    "sector_allocation": {
      "Technology": 45.2,
      "Healthcare": 20.1,
      "Financials": 15.8
    },
    "diversification_score": 0.75,
    "sharpe_ratio": 1.85
  }
}
```

### Trading Operations

**GET** `/trades`

Get trading history and statistics.

**Query Parameters:**
- `symbol` (string): Filter by symbol
- `strategy` (string): Filter by strategy
- `start_date` (ISO date): Start date filter
- `end_date` (ISO date): End date filter
- `limit` (integer): Maximum results (default: 100)

**POST** `/trades`

Execute a manual trade (admin only).

**Request Body:**
```json
{
  "symbol": "AAPL",
  "side": "BUY",
  "quantity": 10,
  "order_type": "LIMIT",
  "price": 150.00,
  "strategy": "manual"
}
```

### Strategy Management

**GET** `/strategies`

Get all trading strategies and their status.

**Response:**
```json
{
  "strategies": [
    {
      "name": "MomentumStrategy",
      "type": "momentum_breakout",
      "enabled": true,
      "active_positions": 3,
      "performance": {
        "total_signals": 125,
        "win_rate": 72.5,
        "total_pnl": 2850.00
      }
    }
  ]
}
```

**PUT** `/strategies/{strategy_name}`

Update strategy configuration.

**Request Body:**
```json
{
  "enabled": true,
  "parameters": {
    "rsi_threshold": 65,
    "volume_threshold": 1.8
  }
}
```

### Market Data

**GET** `/market/analysis`

Get current market analysis and conditions.

**Response:**
```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "overall_sentiment": "moderately_bullish",
  "sentiment_score": 0.68,
  "market_regime": "bull",
  "vix_level": 16.5,
  "leading_sectors": ["Technology", "Healthcare"],
  "trading_recommendation": {
    "recommendation": "moderate_long",
    "position_sizing": "normal",
    "confidence": 0.72
  }
}
```

### Risk Management

**GET** `/risk/metrics`

Get current risk metrics and alerts.

**Response:**
```json
{
  "portfolio_heat": 0.45,
  "current_drawdown": 0.02,
  "total_risk": 2500.00,
  "position_count": 8,
  "sector_exposure": {
    "Technology": 0.45,
    "Healthcare": 0.20
  },
  "risk_violations": [],
  "emergency_mode": false
}
```

**POST** `/risk/emergency-exit`

Trigger emergency exit of all positions (admin only).

### Notifications

**GET** `/notifications`

Get recent notifications and alerts.

**POST** `/notifications/test`

Send a test notification to Telegram.

## Error Responses

All endpoints return consistent error responses:

```json
{
  "error": {
    "code": "INVALID_SYMBOL",
    "message": "Symbol 'INVALID' not found",
    "details": {
      "timestamp": "2024-01-15T10:30:00Z",
      "request_id": "req_123456789"
    }
  }
}
```

## Rate Limits

- **General API**: 1000 requests per hour per API key
- **Trading Operations**: 100 requests per hour per API key
- **Market Data**: 500 requests per hour per API key

Rate limit headers are included in all responses:
- `X-RateLimit-Limit`: Request limit per hour
- `X-RateLimit-Remaining`: Requests remaining
- `X-RateLimit-Reset`: Time when limit resets (Unix timestamp)

## Webhooks

### Schwab Webhook

**POST** `/webhook/schwab`

Receives notifications from Schwab API about order executions, account changes, etc.

### Telegram Webhook

**POST** `/webhook/telegram`

Receives Telegram bot updates for interactive commands.

## WebSocket Endpoints

### Real-time Updates

**WS** `/ws/portfolio`

Real-time portfolio updates.

**WS** `/ws/trades`

Real-time trade execution updates.

**WS** `/ws/market`

Real-time market data and analysis updates.

## SDK Examples

### Python SDK

```python
import aiohttp
import asyncio

class TradingBotAPI:
    def __init__(self, base_url, api_token):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {api_token}',
            'Content-Type': 'application/json'
        }
    
    async def get_portfolio(self):
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f'{self.base_url}/portfolio',
                headers=self.headers
            ) as response:
                return await response.json()

# Usage
api = TradingBotAPI('https://your-domain.com/api', 'your_api_token')
portfolio = await api.get_portfolio()
```

### JavaScript SDK

```javascript
class TradingBotAPI {
  constructor(baseUrl, apiToken) {
    this.baseUrl = baseUrl;
    this.headers = {
      'Authorization': `Bearer ${apiToken}`,
      'Content-Type': 'application/json'
    };
  }
  
  async getPortfolio() {
    const response = await fetch(`${this.baseUrl}/portfolio`, {
      headers: this.headers
    });
    return response.json();
  }
  
  async getMarketAnalysis() {
    const response = await fetch(`${this.baseUrl}/market/analysis`, {
      headers: this.headers
    });
    return response.json();
  }
}

// Usage
const api = new TradingBotAPI('https://your-domain.com/api', 'your_api_token');
const portfolio = await api.getPortfolio();
```

## Security

- All API endpoints use HTTPS in production
- API keys are required for authentication
- Rate limiting prevents abuse
- Input validation on all parameters
- SQL injection protection
- CORS headers configured appropriately

## Monitoring

- All API calls are logged with structured logging
- Performance metrics are tracked
- Error rates are monitored
- Alerts are sent for unusual activity

## Support

For API support and questions:
- GitHub Issues: https://github.com/HectorTa1989/schwab-telegram-trading-bot/issues
- Documentation: https://schwab-trading-bot.com/docs
- Email: <EMAIL>
