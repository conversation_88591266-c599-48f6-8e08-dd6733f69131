"""
Momentum Breakout Strategy
Identifies stocks breaking out of consolidation patterns with strong momentum.

Author: HectorTa1989
GitHub: https://github.com/HectorTa1989/schwab-telegram-trading-bot
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
import structlog
import numpy as np
from base_strategy import BaseStrategy

logger = structlog.get_logger(__name__)


class MomentumStrategy(BaseStrategy):
    """
    Momentum Breakout Strategy Implementation
    
    Strategy Logic:
    1. Identify stocks breaking above 20-day high
    2. Confirm with volume spike (>1.5x average)
    3. RSI > 60 and increasing
    4. Price above 50-day moving average
    5. Stop loss at 2% or 20-day low
    6. Take profit at 2:1 risk/reward ratio
    """
    
    def __init__(self, config: Dict[str, Any], schwab_client, market_analyzer, risk_manager):
        """Initialize momentum strategy."""
        super().__init__(
            config=config,
            schwab_client=schwab_client,
            market_analyzer=market_analyzer,
            risk_manager=risk_manager,
            name="MomentumStrategy"
        )
        
        # Strategy parameters
        self.lookback_period = config.get('lookback_period', 20)
        self.volume_threshold = config.get('volume_threshold', 1.5)
        self.rsi_threshold = config.get('rsi_threshold', 60)
        self.ma_period = config.get('ma_period', 50)
        self.min_price = config.get('min_price', 10)
        self.max_price = config.get('max_price', 500)
        self.stop_loss_pct = config.get('stop_loss_pct', 0.02)
        self.take_profit_ratio = config.get('take_profit_ratio', 2.0)
        
        # Watchlist - in production, this would come from a screener
        self.watchlist = [
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA',
            'NVDA', 'META', 'NFLX', 'AMD', 'CRM',
            'ADBE', 'PYPL', 'INTC', 'CSCO', 'ORCL'
        ]
        
        # Signal cooldown to prevent duplicate signals
        self.signal_cooldown = timedelta(minutes=15)
        self.last_signals: Dict[str, datetime] = {}
        
        logger.info(
            "✅ Momentum strategy initialized",
            lookback_period=self.lookback_period,
            volume_threshold=self.volume_threshold,
            rsi_threshold=self.rsi_threshold,
            watchlist_size=len(self.watchlist)
        )
    
    async def generate_signals(self) -> List[Dict[str, Any]]:
        """Generate momentum breakout signals."""
        if not self.is_enabled:
            return []
        
        signals = []
        
        try:
            # Get market data for watchlist
            market_data = await self.get_market_data(self.watchlist)
            
            if not market_data:
                logger.warning("⚠️ No market data received")
                return []
            
            # Analyze each symbol
            for symbol in self.watchlist:
                try:
                    signal = await self._analyze_symbol(symbol, market_data.get(symbol))
                    if signal:
                        signals.append(signal)
                        await self.record_signal(signal)
                        
                except Exception as e:
                    logger.error(f"❌ Error analyzing {symbol}", error=str(e))
                    continue
            
            logger.info(f"🎯 Generated {len(signals)} momentum signals")
            return signals
            
        except Exception as e:
            logger.error("❌ Error generating momentum signals", error=str(e), exc_info=True)
            return []
    
    async def _analyze_symbol(self, symbol: str, quote_data: Dict) -> Optional[Dict[str, Any]]:
        """Analyze individual symbol for momentum breakout."""
        if not quote_data:
            return None
        
        try:
            # Check signal cooldown
            if self._is_in_cooldown(symbol):
                return None
            
            # Get current price and volume
            current_price = quote_data.get('last', 0)
            current_volume = quote_data.get('volume', 0)
            
            # Basic price filters
            if not (self.min_price <= current_price <= self.max_price):
                return None
            
            # Get technical indicators (simplified for demo)
            indicators = await self.calculate_technical_indicators(symbol, self.lookback_period)
            
            # Check momentum conditions
            momentum_score = await self._calculate_momentum_score(
                symbol, current_price, current_volume, indicators
            )
            
            if momentum_score >= 0.7:  # 70% confidence threshold
                return await self._create_buy_signal(
                    symbol, current_price, indicators, momentum_score
                )
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error analyzing {symbol}", error=str(e))
            return None
    
    async def _calculate_momentum_score(
        self,
        symbol: str,
        current_price: float,
        current_volume: int,
        indicators: Dict[str, float]
    ) -> float:
        """Calculate momentum score based on multiple factors."""
        
        score_components = []
        
        try:
            # 1. Price breakout score (25% weight)
            sma_20 = indicators.get('sma_20', current_price)
            if current_price > sma_20 * 1.02:  # 2% above 20-day high
                breakout_score = min(1.0, (current_price - sma_20) / sma_20 / 0.05)  # Max at 5% breakout
                score_components.append(('breakout', breakout_score, 0.25))
            else:
                score_components.append(('breakout', 0.0, 0.25))
            
            # 2. Volume confirmation score (20% weight)
            volume_ratio = indicators.get('volume_ratio', 1.0)
            if volume_ratio >= self.volume_threshold:
                volume_score = min(1.0, volume_ratio / 3.0)  # Max at 3x volume
                score_components.append(('volume', volume_score, 0.20))
            else:
                score_components.append(('volume', 0.0, 0.20))
            
            # 3. RSI momentum score (20% weight)
            rsi = indicators.get('rsi', 50)
            if rsi >= self.rsi_threshold:
                rsi_score = min(1.0, (rsi - 50) / 30)  # Scale from 50-80
                score_components.append(('rsi', rsi_score, 0.20))
            else:
                score_components.append(('rsi', 0.0, 0.20))
            
            # 4. Moving average trend score (20% weight)
            sma_50 = indicators.get('sma_50', current_price)
            if current_price > sma_50:
                ma_score = min(1.0, (current_price - sma_50) / sma_50 / 0.1)  # Max at 10% above MA
                score_components.append(('ma_trend', ma_score, 0.20))
            else:
                score_components.append(('ma_trend', 0.0, 0.20))
            
            # 5. MACD momentum score (15% weight)
            macd = indicators.get('macd', 0)
            if macd > 0:
                macd_score = min(1.0, abs(macd) / 2.0)  # Normalize MACD
                score_components.append(('macd', macd_score, 0.15))
            else:
                score_components.append(('macd', 0.0, 0.15))
            
            # Calculate weighted score
            total_score = sum(score * weight for _, score, weight in score_components)
            
            logger.debug(
                f"📊 Momentum score calculated",
                symbol=symbol,
                total_score=total_score,
                components=score_components
            )
            
            return total_score
            
        except Exception as e:
            logger.error(f"❌ Error calculating momentum score for {symbol}", error=str(e))
            return 0.0
    
    async def _create_buy_signal(
        self,
        symbol: str,
        current_price: float,
        indicators: Dict[str, float],
        confidence: float
    ) -> Dict[str, Any]:
        """Create a buy signal with stop loss and take profit levels."""
        
        # Calculate stop loss (2% or 20-day low, whichever is higher)
        stop_loss_price = max(
            current_price * (1 - self.stop_loss_pct),
            indicators.get('sma_20', current_price) * 0.98
        )
        
        # Calculate take profit (2:1 risk/reward)
        risk_amount = current_price - stop_loss_price
        take_profit_price = current_price + (risk_amount * self.take_profit_ratio)
        
        signal = {
            'symbol': symbol,
            'side': 'BUY',
            'order_type': 'LIMIT',
            'entry_price': current_price,
            'stop_loss': stop_loss_price,
            'take_profit': take_profit_price,
            'confidence': confidence,
            'reason': f'Momentum breakout - Score: {confidence:.2f}',
            'strategy_data': {
                'lookback_period': self.lookback_period,
                'volume_threshold': self.volume_threshold,
                'rsi_threshold': self.rsi_threshold,
                'indicators': indicators
            }
        }
        
        # Update signal cooldown
        self.last_signals[symbol] = datetime.now()
        
        logger.info(
            f"🎯 Momentum buy signal generated",
            symbol=symbol,
            price=current_price,
            confidence=confidence,
            stop_loss=stop_loss_price,
            take_profit=take_profit_price
        )
        
        return signal
    
    async def should_exit_position(self, symbol: str, position_data: Dict) -> Tuple[bool, str]:
        """Determine if a momentum position should be exited."""
        try:
            # Get current market data
            market_data = await self.get_market_data([symbol])
            if not market_data or symbol not in market_data:
                return False, "No market data available"
            
            current_price = market_data[symbol].get('last', 0)
            entry_price = position_data.get('avg_cost', 0)
            
            if not current_price or not entry_price:
                return False, "Invalid price data"
            
            # Check stop loss
            stop_loss = position_data.get('stop_loss')
            if stop_loss and current_price <= stop_loss:
                return True, f"Stop loss triggered at {current_price}"
            
            # Check take profit
            take_profit = position_data.get('take_profit')
            if take_profit and current_price >= take_profit:
                return True, f"Take profit reached at {current_price}"
            
            # Check for momentum reversal
            indicators = await self.calculate_technical_indicators(symbol)
            rsi = indicators.get('rsi', 50)
            
            # Exit if RSI drops below 40 (momentum weakening)
            if rsi < 40:
                return True, f"Momentum weakening - RSI: {rsi:.1f}"
            
            # Check for significant time decay (hold for max 10 days)
            entry_date = position_data.get('entry_date')
            if entry_date:
                days_held = (datetime.now() - entry_date).days
                if days_held >= 10:
                    return True, f"Maximum hold period reached ({days_held} days)"
            
            return False, "Hold position"
            
        except Exception as e:
            logger.error(f"❌ Error checking exit condition for {symbol}", error=str(e))
            return False, f"Error checking exit: {str(e)}"
    
    def _is_in_cooldown(self, symbol: str) -> bool:
        """Check if symbol is in signal cooldown period."""
        last_signal_time = self.last_signals.get(symbol)
        if not last_signal_time:
            return False
        
        return datetime.now() - last_signal_time < self.signal_cooldown
    
    async def _strategy_specific_validation(self, signal: Dict[str, Any]) -> Tuple[bool, str]:
        """Momentum strategy specific validation."""
        try:
            # Check if we already have a position in this symbol
            if signal['symbol'] in self.active_positions:
                return False, "Already have position in this symbol"
            
            # Validate confidence threshold
            if signal.get('confidence', 0) < 0.6:
                return False, f"Confidence too low: {signal.get('confidence', 0):.2f}"
            
            # Check market conditions (simplified)
            # In production, you'd check VIX, market trend, etc.
            
            return True, "Momentum validation passed"
            
        except Exception as e:
            logger.error("❌ Error in momentum validation", error=str(e))
            return False, f"Validation error: {str(e)}"
    
    def get_strategy_info(self) -> Dict[str, Any]:
        """Get strategy information and current state."""
        return {
            'name': self.name,
            'type': 'momentum_breakout',
            'enabled': self.is_enabled,
            'parameters': {
                'lookback_period': self.lookback_period,
                'volume_threshold': self.volume_threshold,
                'rsi_threshold': self.rsi_threshold,
                'ma_period': self.ma_period,
                'stop_loss_pct': self.stop_loss_pct,
                'take_profit_ratio': self.take_profit_ratio
            },
            'watchlist_size': len(self.watchlist),
            'active_positions': len(self.active_positions),
            'performance': self.get_performance_metrics(),
            'last_signal_time': self.last_signal_time
        }
